package com.rs.module.base.service.zh.staffduty;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.base.controller.admin.zh.vo.staffduty.*;
import com.rs.module.base.dao.zh.StaffDutyPostDao;
import com.rs.module.base.dao.zh.StaffDutyRoleDao;
import com.rs.module.base.dao.zh.StaffDutyRoleTimeDao;
import com.rs.module.base.dao.zh.StaffDutyTemplateDao;
import com.rs.module.base.entity.zh.StaffDutyPostDO;
import com.rs.module.base.entity.zh.StaffDutyRoleDO;
import com.rs.module.base.entity.zh.StaffDutyRoleTimeDO;
import com.rs.module.base.entity.zh.StaffDutyTemplateDO;
import com.rs.module.base.util.StaffDutyCoderUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 */
@Slf4j
@Service
public class StaffDutyTemplateServiceImpl extends BaseServiceImpl<StaffDutyTemplateDao, StaffDutyTemplateDO> implements StaffDutyTemplateService {

    @Resource
    private StaffDutyTemplateDao staffDutyTemplateDao;

    @Resource
    private StaffDutyPostDao staffDutyPostDao;

    @Resource
    private StaffDutyRoleDao dutyRoleDao;

    @Resource
    private StaffDutyRoleTimeDao dutyRoleTimeDao;

    @Override
    public StaffDutyTemplateRespVO info(String id) {
        StaffDutyTemplateRespVO info = staffDutyTemplateDao.info(id);
        return info;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer add(StaffDutyTemplateSaveReqVO dto,Integer templateType) {
        //校验模板名字是否重复
        List<Integer> checkNameList = staffDutyTemplateDao.checkName(dto.getName(), templateType);
        if (!checkNameList.isEmpty()) {
            return -1;
        }
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        StaffDutyTemplateDO entity = new StaffDutyTemplateDO();
        BeanUtils.copyProperties(dto, entity);
        entity.setOrgCode(sessionUser.getOrgCode());
        entity.setStatus("0");
        entity.setType(templateType);

        //先插入模板表
        int result = staffDutyTemplateDao.insert(entity);
        if (result > 0) {
            //模板新增成功  插入关联的岗位列表
            List<StaffDutyPostSaveReqVO> dutyPostDTOS = dto.getDutyPostDTOS();
            if (!CollectionUtils.isEmpty(dutyPostDTOS)) {
                for (StaffDutyPostSaveReqVO dutyPostDTO : dutyPostDTOS) {
                    //校验参数
                    dutyPostDTO.setTempId(entity.getId());
                    StaffDutyPostDO dutyPostEntity = new StaffDutyPostDO();
                    BeanUtils.copyProperties(dutyPostDTO, dutyPostEntity);
                    int postResult = this.staffDutyPostDao.insert(dutyPostEntity);
                    if (postResult > 0) {
                        //模板新增成功  插入关联的角色列表
                        List<StaffDutyRoleSaveReqVO> dutyRoleDTOS = dutyPostDTO.getDutyRoleDTOS();
                        if (!CollectionUtils.isEmpty(dutyRoleDTOS)) {
                            for (StaffDutyRoleSaveReqVO dutyRoleDTO : dutyRoleDTOS) {
                                //校验参数
                                dutyRoleDTO.setPostId(dutyPostEntity.getId());
                                StaffDutyRoleDO dutyRoleEntity = new StaffDutyRoleDO();
                                BeanUtils.copyProperties(dutyRoleDTO, dutyRoleEntity);
                                int roleInsert = this.dutyRoleDao.insert(dutyRoleEntity);
                                if (roleInsert > 0) {
                                    //角色新增呈贡 插入时间列表
                                    List<StaffDutyRoleTimeSaveReqVO> roleTimeDTOS = dutyRoleDTO.getRoleTimeDTOS();
                                    for (StaffDutyRoleTimeSaveReqVO roleTimeDTO : roleTimeDTOS) {
                                        //校验参数
                                        roleTimeDTO.setDutyRoleId(dutyRoleEntity.getId());
                                        roleTimeDTO.setPostKey("DUTY" + StaffDutyCoderUtil.getOrderCode());
                                        StaffDutyRoleTimeDO dutyRoleTimeEntity = new StaffDutyRoleTimeDO();
                                        BeanUtils.copyProperties(roleTimeDTO, dutyRoleTimeEntity);
                                        dutyRoleTimeEntity.setDutyTimeEnd(roleTimeDTO.getDutyTimeEnd());//LocalTime.parse(roleTimeDTO.getDutyTimeEnd())
                                        dutyRoleTimeEntity.setDutyTimeBegin(roleTimeDTO.getDutyTimeBegin());
                                        this.dutyRoleTimeDao.insert(dutyRoleTimeEntity);
                                    }
                                }
                            }
                        }
                    }
                    //岗位新增成功  插入关联的角色列表
                }
            }
        }
        return 1;
    }

    @Override
    public Integer tempOpen(String id, Integer status,Integer templateType) {
        //设置启动模板
        if (status == 1) {
            //启用模板将清除今后的排班数据
            staffDutyTemplateDao.delRecord();
            staffDutyTemplateDao.delPersonByByRecordId();
            //启用模板停止其它正在启动的模板
            staffDutyTemplateDao.initStatus(templateType);
            StaffDutyTemplateDO entity = new StaffDutyTemplateDO();
            entity.setId(id);
            entity.setStatus("1");
            staffDutyTemplateDao.updateById(entity);
        } else if (status == 0) {
            StaffDutyTemplateDO entity = new StaffDutyTemplateDO();
            entity.setId(id);
            entity.setStatus("0");
            staffDutyTemplateDao.updateById(entity);
        }
        return 1;
    }

    @Override
    public Boolean tempOpenCheck(String id) {
        List<Integer> result = staffDutyTemplateDao.tempOpenCheck();
        if (result.isEmpty()) {
            return false;
        } else {
            return true;
        }
    }

    @Override
    public Integer delete(String[] ids) {
        for (String id : ids) {
            //校验 只有“停用”状态的模板才能删除
            StaffDutyTemplateDO tempEntity = staffDutyTemplateDao.selectById(id);
            if ("1".equals(tempEntity.getStatus())) {
                return -1;
            }
            this.staffDutyTemplateDao.deleteById(tempEntity);
        }
        return 1;
    }

    /**
     * 更新
     * @param dto
     * @return
     */
    @Override
    public Integer updateInfo(StaffDutyTemplateSaveReqVO dto,Integer templateType) {
        if (StringUtil.isEmpty(dto.getId())) {
            throw new IllegalArgumentException("更新接口ID不能为空！");
        }
        StaffDutyTemplateDO entityOld = staffDutyTemplateDao.selectById(dto.getId());
        Assert.notNull(entityOld, "传入ID异常");

        //校验模板名字是否重复  也需要更加类型来去重
        int count = staffDutyTemplateDao.checkNameById(dto.getName(), dto.getId(), templateType);
        if (count > 0) {
            return -1;
        }
        //dto.setStatus(1);
        StaffDutyTemplateDO entity = new StaffDutyTemplateDO();
        BeanUtils.copyProperties(dto, entity);
        entity.setId(entityOld.getId());
        entity.setStatus(entityOld.getStatus());
        entity.setType(templateType);

        //先插入模板表
        int result = staffDutyTemplateDao.updateById(entity);
        if (result > 0) {
            //模板编辑成功  插入关联的岗位列表
            List<StaffDutyPostSaveReqVO> dutyPostDTOS = dto.getDutyPostDTOS();
            if (!CollectionUtils.isEmpty(dutyPostDTOS)) {
                for (StaffDutyPostSaveReqVO dutyPostDTO : dutyPostDTOS) {
                    //校验参数
                    dutyPostDTO.setTempId(entity.getId());
                    StaffDutyPostDO dutyPostEntity = new StaffDutyPostDO();
                    BeanUtils.copyProperties(dutyPostDTO, dutyPostEntity);
                    //先干掉之前的，再插入
                    staffDutyPostDao.delete(new LambdaQueryWrapper<StaffDutyPostDO>().eq(StaffDutyPostDO::getTempId, entity.getId()));


                    int postResult = this.staffDutyPostDao.insert(dutyPostEntity);
                    if (postResult > 0) {
                        //模板新增成功  插入关联的角色列表
                        List<StaffDutyRoleSaveReqVO> dutyRoleDTOS = dutyPostDTO.getDutyRoleDTOS();
                        if (!CollectionUtils.isEmpty(dutyRoleDTOS)) {
                            //更新删除再插入
                            List<StaffDutyRoleDO> dutyRoleEntityList = dutyRoleDao.selectList(new LambdaQueryWrapper<StaffDutyRoleDO>().in(StaffDutyRoleDO::getPostId, dutyPostEntity.getId()));
                            dutyRoleDao.delete(new LambdaQueryWrapper<StaffDutyRoleDO>().in(StaffDutyRoleDO::getPostId, dutyPostEntity.getId()));
                            if(!CollectionUtils.isEmpty(dutyRoleEntityList)){
                                //删除关联角色
                                dutyRoleTimeDao.delete(new LambdaQueryWrapper<StaffDutyRoleTimeDO>().in(StaffDutyRoleTimeDO::getDutyRoleId, dutyRoleEntityList.stream().map(StaffDutyRoleDO::getId).collect(Collectors.toList())));
                            }

                            for (StaffDutyRoleSaveReqVO dutyRoleDTO : dutyRoleDTOS) {
                                //校验参数
                                dutyRoleDTO.setPostId(dutyPostEntity.getId());
                                StaffDutyRoleDO dutyRoleEntity = new StaffDutyRoleDO();
                                BeanUtils.copyProperties(dutyRoleDTO, dutyRoleEntity);
                                int roleInsert = this.dutyRoleDao.insert(dutyRoleEntity);
                                if (roleInsert > 0) {
                                    //角色新增呈贡 插入时间列表
                                    List<StaffDutyRoleTimeSaveReqVO> roleTimeDTOS = dutyRoleDTO.getRoleTimeDTOS();
                                    for (StaffDutyRoleTimeSaveReqVO roleTimeDTO : roleTimeDTOS) {
                                        //校验参数
                                        roleTimeDTO.setDutyRoleId(dutyRoleEntity.getId());
                                        roleTimeDTO.setPostKey("DUTY" + StaffDutyCoderUtil.getOrderCode());
                                        StaffDutyRoleTimeDO dutyRoleTimeEntity = new StaffDutyRoleTimeDO();
                                        BeanUtils.copyProperties(roleTimeDTO, dutyRoleTimeEntity);
                                        dutyRoleTimeEntity.setDutyTimeEnd(roleTimeDTO.getDutyTimeEnd());
                                        dutyRoleTimeEntity.setDutyTimeBegin(roleTimeDTO.getDutyTimeBegin());
                                        this.dutyRoleTimeDao.insert(dutyRoleTimeEntity);
                                    }
                                }
                            }
                        }
                    }
                    //岗位新增成功  插入关联的角色列表
                }
            }
        }

        return 1;

    }

}
