package com.rs.enums;


import com.baomidou.mybatisplus.core.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 机构类型字典
*<AUTHOR>
*@email <EMAIL>
*@date 2022/10/9
*/

@Getter
public enum PrisonTypeEnum implements IEnum<String> {
    KSS("01", "看守所", 0),
    <PERSON><PERSON>("02", "拘留所", 0),
    J<PERSON>("03", "戒毒所", 0),
    YLS("04", "医疗所", 0),
    JGZD("05","监管支队",1),
    JGBQ("06","监管总队", 0);
    private String value;
    private String desc;
    // 是否是支队
    private Integer isZd;

    PrisonTypeEnum(final String value, final String desc, final Integer isZd) {
        this.desc = desc;
        this.value = value;
        this.isZd = isZd;
    }

    @Override
    public String getValue() {
        return value;
    }

    public Integer isIsZd() {
        return isZd;
    }

    @JsonValue
    public String getDesc() {
        return desc;
    }

    public static String getValue(String desc) {
        for (PrisonTypeEnum e : PrisonTypeEnum.values()) {
            if (e.desc.equals(desc)) {
                return e.value;
            }
        }
        return null;
    }

    public static String getDesc(String value) {
        for (PrisonTypeEnum e : PrisonTypeEnum.values()) {
            if (e.value.equals(value)) {
                return e.desc;
            }
        }
        return null;
    }

    public static Integer isIsZd(String value) {
        for (PrisonTypeEnum e : PrisonTypeEnum.values()) {
            if (e.value.equals(value)) {
                return e.isZd;
            }
        }
        return 0;
    }
}
