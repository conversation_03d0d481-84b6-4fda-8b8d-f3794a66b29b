package com.rs.config;

import java.util.Properties;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaVendorAdapter;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;

/**
 * @ClassName JpaConfig
 * @Description Jpa配置类
 * <AUTHOR>
 * @Date 2025/5/14 20:58
 * @Version 1.0
 */
@EntityScan("com.rs.*")
@EnableJpaRepositories("com.rs.*")
@Configuration
public class JpaConfig {
	
	//主数据库方言
	@Value("${conf.datasource.dynamic.master.dialect:org.hibernate.dialect.PostgreSQLDialect}")
	private String primaryDialect;
	
	//是否展示sql
	@Value("${conf.datasource.dynamic.master.show-sql:true}")
	private Boolean showSql;
	
	//是否格式化sql
	@Value("${conf.datasource.dynamic.master.format-sql:true}")
	private Boolean formatSql;
	
    @Bean
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(DataSource dataSource, JpaVendorAdapter jpaVendorAdapter) {
        LocalContainerEntityManagerFactoryBean lef = new LocalContainerEntityManagerFactoryBean();
        lef.setDataSource(dataSource);
        lef.setJpaVendorAdapter(jpaVendorAdapter);
        lef.setPackagesToScan("com.rs");
        Properties jpaProperties = new Properties();
        jpaProperties.put("hibernate.dialect", primaryDialect);
        jpaProperties.put("hibernate.hbm2ddl.auto", "none");
        jpaProperties.put("hibernate.show_sql", showSql);
        jpaProperties.put("hibernate.format_sql", formatSql);
        jpaProperties.put("hibernate.physical_naming_strategy", "org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy");
        lef.setJpaProperties(jpaProperties);
        
        return lef;
    }
}
