package com.rs.util;

import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.rs.framework.mybatis.util.BspDbUtil;
import lombok.extern.log4j.Log4j2;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName PDFUtil
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/6/5 18:12
 * @Version 1.0
 */
@Log4j2
public class PDFUtil {

    public static byte[] getPdf(String templateName, Object json) {
        String url = BspDbUtil.getParam("BSP_PRINT");
        Map<String, Object> paramMap = new HashMap<>();
        String filename = System.getProperty("user.dir")+ File.separator+"config"+File.separator+"template"+File.separator+ templateName;
        paramMap.put("file", new File(filename));
        JSONConfig jsonConfig = new JSONConfig();
        jsonConfig.setDateFormat("yyyy-MM-dd HH:mm:ss");
        String jsonStr = JSONUtil.toJsonStr(json, jsonConfig);
        log.info(jsonStr);
        paramMap.put("json", jsonStr);
        return HttpRequest.post(url)
                .form(paramMap)
                .execute()
                .bodyBytes();

    }
}
