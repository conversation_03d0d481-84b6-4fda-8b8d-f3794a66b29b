package com.rs.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.rs.module.base.util.BspApprovalUtil;
import com.rs.module.base.vo.ApproveTraceVO;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class ApprovalTraceUtil {


    /**
     * 获取审批意见意见信息
     * <AUTHOR>
     * @date 2025/7/7 16:35
     * @param [actInstid]
     * @return java.util.List<com.rs.module.base.vo.ApproveTraceVO>
     */
    public static List<ApproveTraceVO> converBspApprovalResult(String actInstid){
        if(StrUtil.isNotBlank(actInstid)){
            return new ArrayList<>();
        }
        List<ApproveTraceVO> list = new ArrayList<>();
        JSONObject bspApprovalTrack = BspApprovalUtil.getBpmApi().approveTrack(actInstid);

        if (bspApprovalTrack == null || !bspApprovalTrack.getBoolean("success")) {
            log.error("请求审批结果出错");
            return null;
        }

        JSONArray data = bspApprovalTrack.getJSONArray("data");
        if (data == null || data.isEmpty()) {
            log.error("请求审批结果为空");
            return null;
            //throw new RuntimeException("请求审批结果为空");
        }

        for (Object obj: data) {
            JSONObject track = JSON.parseObject(JSON.toJSONString(obj));
            String taskName = track.getString("taskName");
            if(taskName.contains("审批")){
                ApproveTraceVO approvalTraceVO = new ApproveTraceVO();
                approvalTraceVO.setNodeName(taskName);
                if(StrUtil.isBlank( track.getString("createTime"))){
                    continue;
                }

                String approverXm = track.getString("executeUserName");
                if(StrUtil.isNotBlank(approverXm )){
                    approvalTraceVO.setApproverXm(approverXm );
                    String endTime = track.getString("endTime");
                    if(StrUtil.isNotBlank(endTime) ){
                        endTime = endTime.substring(0,19);
                    }
                    approvalTraceVO.setApproverTime(DateUtil.parseDateTime(endTime));
                    approvalTraceVO.setApprovalComments(track.getString("approvalContent"));

                    String approvalResultName = "1".equals(track.getString("isApprove"))?"同意":"不同意";
                    approvalTraceVO.setApprovalResult(track.getString("isApprove"));
                    approvalTraceVO.setApprovalResultName(approvalResultName);
                }
            }
        }
        return list;
    }


    //    "taskKey": "sid-EA0F98F6-34F4-4E0F-A846-0EB4F68D3E2F",
    //            "executeUserName": "超级管理员",
    //            "businessId": "1931238671315636224",
    //            "mobile": "***********",
    //            "approvalContent": "通过",
    //            "isApprove": "5",
    //            "duration": 0,
    //            "actDefId": "xinxiyuanbujianshenqing:1:1927538417142206464",
    //            "executeCityId": "000000",
    //            "czpt": 0,
    //            "executeOrgId": "000000000000",
    //            "createTime": "2025-06-07 14:35:54:925",
    //            "executeUserId": "512501196512305186",
    //            "actInstId": "1931238672505376768",
    //            "executeRegId": "000000",
    //            "taskName": "单位审批",
    //            "id": "1931238692197634048",
    //            "executeOrgName": "示例机构",
    //            "endTime": "2025-06-07 14:35:59:000",
    //            "taskId": "1931238672757035008",
    //            "status": 5
}
