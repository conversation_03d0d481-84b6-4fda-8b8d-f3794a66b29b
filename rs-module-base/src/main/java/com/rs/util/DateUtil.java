package com.rs.util;

import cn.hutool.core.date.DateTime;
import com.rs.framework.common.exception.BizException;
import org.springframework.util.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * 日期操作工具类
 *
 * <AUTHOR>
 */
public class DateUtil {


	/**
	 * 横杠形时间格式化模式 yyyy-MM-dd HH:mm:ss
	 */
	public static final String DATE_PATTERN = "yyyyMMdd";
	public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(DATE_PATTERN);

	/**
	 * 横杠形时间格式化模式 yyyy-MM-dd HH:mm:ss
	 */
	public static final String DATE_TIME_PATTERN_OF_BAR = "yyyy-MM-dd HH:mm:ss";

	/**
	 * 标准时间格式(包含毫秒部分）
	 */
	public static final String STANDARD_TIME_MILLS_PATTERN = "yyyy-MM-dd HH:mm:ss.SSS";

	/**
	 * 东八区
	 */
	public static final String GMT8 = "GMT+8";

	/**
	 * 时间格式化模式 yyyyMMddHHmmss
	 */
	public static final String DATE_TIME_PATTERN = "yyyyMMddHHmmss";
	public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(DATE_TIME_PATTERN);

	/**
	 * 日期格式化模式 yyyy-MM-dd
	 */
	public static final String DATE_PATTERN_OF_BAR = "yyyy-MM-dd";

	/**
	 * 时间格式
	 */
	public static final String TIME_PATTERN_DEFAULT = "HH:mm:ss";
	public static final DateTimeFormatter TIME_FORMATTER_DEFAULT = DateTimeFormatter.ofPattern(TIME_PATTERN_DEFAULT);

	/**
	 * 获取当前系统日期
	 */
	public static Date getCurrentDate() {
		return new Date();
	}


	/**
	 * 时区
	 */
	public static final ZoneOffset ZONE_OFFSET = ZoneOffset.ofHours(8);
	public static final ZoneOffset ZONE_OFFSET_ZERO = ZoneOffset.ofHours(0);

	/**
	 * 横杠形时间格式化模式 yyyy-MM-dd HH:mm:ss
	 */
	public static final String DATE_TIME_PATTERN_OF_CN = "yyyy-MM-dd HH:mm:ss";

	public static final DateTimeFormatter DATE_TIME_FORMATTER_OF_CN =
			DateTimeFormatter.ofPattern(DATE_TIME_PATTERN_OF_CN);

	public static final DateTimeFormatter DATE_FORMATTER_OF_CN =
			DateTimeFormatter.ofPattern(DATE_PATTERN_OF_BAR);

	public static final DateTimeFormatter DATE_NOT_SPLIT_FORMATTER_OF_CN =
			DateTimeFormatter.ofPattern(DATE_PATTERN);

	/**
	 * 字符串转成日期
	 */
	public static Date stringToDateTime(String date, String pattern) {
		if (StringUtils.isEmpty(date)) {
			return null;
		}
		try {
			if (StringUtils.isEmpty(pattern)) {
				pattern = DATE_TIME_PATTERN;
			} else if (pattern.contains("-")) {
				pattern = DATE_TIME_PATTERN_OF_BAR;
			}
			return new SimpleDateFormat(pattern).parse(date);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}


	/**
	 * 字符串转成日期
	 */
	public static Date dateToDateTime(Date date, String pattern) {
		if (StringUtils.isEmpty(date)) {
			return null;
		}
		try {
			if (StringUtils.isEmpty(pattern)) {
				pattern = DATE_PATTERN;
			} else if (pattern.contains("-")) {
				pattern = DATE_PATTERN_OF_BAR;
			}
			String format = new SimpleDateFormat(pattern).format(date);
			return new SimpleDateFormat(pattern).parse(format);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 获取当前日期字符串（无符号）
	 *
	 * @return
	 */
	public static String nowNonDelimiterDateStr() {
		return now().format(DATE_FORMATTER);
	}


	/**
	 * 获取当前日期
	 *
	 * @return
	 */
	public static LocalDateTime now() {
		return LocalDateTime.now();
	}


	/**
	 * 对date进行加法运算
	 *
	 * @param date 传入的Date实例
	 * @param type 计算类型：
	 *             Calendar.YEAR/MONTH/WEEK_OF_YEAR/WEEK_OF_MONTH/DATE等枚举
	 * @param num  具体的值，如果传入为正数，则进行加法运算，否则进行减法法运算
	 * @return 如果date为null，则返回null，否则返回计算后的Date实例
	 */
	public static Date add(Date date, int type, int num) {
		if (date == null) {
			return null;
		}
		if (num == 0) {
			return date;
		}
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		cal.set(type, cal.get(type) + num);
		return cal.getTime();
	}

	/**
	 * 获取一段时间的集合
	 *
	 * @param date 传入的Date实例
	 * @param type 计算类型：
	 *             Calendar.YEAR/MONTH/WEEK_OF_YEAR/WEEK_OF_MONTH/DATE等枚举
	 * @param num  具体的值，如果传入为正数，则进行加法运算，否则进行减法法运算
	 * @param step 步长
	 * @return List
	 */
	public static List<Date> stepDate(Date date, int type, int num, int step) {
		Objects.requireNonNull(date);
		int adjustNum = Math.abs(num);
		int adjustStep = num < 0 ? -1 * step : step;
		List<Date> dates = new ArrayList<>();
		dates.add(date);
		for (int i = 0, j = 0; i < adjustNum && j < dates.size(); i = i + step, j += 1) {
			dates.add(add(dates.get(j), type, adjustStep));
		}
		dates.sort(Comparator.comparingLong(Date::getTime));
		return dates;
	}

	/**
	 * 获取一段时间的集合（升序）
	 *
	 * @param startDate 传入的起始Date实例
	 * @param endDate   传入的结束Date实例
	 * @param type      计算类型：
	 *                  Calendar.YEAR/MONTH/WEEK_OF_YEAR/WEEK_OF_MONTH/DATE等枚举
	 * @param step      步长
	 * @return List
	 */
	public static List<Date> stepDate(Date startDate, Date endDate, int type, int step) {
		if (step <= 0) {
			throw new BizException(100110005,"内部错误，请联系管理员");
		}
		Objects.requireNonNull(startDate);
		Objects.requireNonNull(endDate);
		List<Date> list = new ArrayList<>();
		list.add(startDate);
		Calendar cal = Calendar.getInstance();
		cal.setTime(startDate);
		long end = endDate.getTime();
		long cursor = startDate.getTime();
		while (cursor < end) {
			cal.add(type, step);
			list.add(cal.getTime());
			cursor = cal.getTimeInMillis();
		}
		return list;
	}

	/**
	 * 返回时间格式字符串
	 *
	 * @param pattern 指定格式化模式（如：yyyy-MM-dd、yyyy-MM-dd HH:mm:ss）
	 * @return 格式化后的字符串
	 * 如果指定格式，则默认返回yyyy-MM-dd HH:mm:ss时间格式
	 * 如果根据指定模式格式化失败则返回 null
	 */
	public static String format(Date date, String pattern) {
		if (date == null) {
			return null;
		}
		String formatPattern = pattern;
		if (formatPattern == null || "".equals(formatPattern)) {
			formatPattern = DATE_TIME_PATTERN;
		}
		try {
			return new SimpleDateFormat(formatPattern).format(date);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 计算两个时间字符串（yyyy-MM-dd HH:mm:ss格式）的时间差（单位为秒）
	 */
	public static long getTimeDifference(String beginTime, String endTime) {
		if (isEmpty(beginTime) || isEmpty(endTime)) {
			return 0;
		}
		Date beginDate = stringToDateTime(beginTime, DATE_TIME_PATTERN_OF_BAR);
		Date endDate = stringToDateTime(endTime, DATE_TIME_PATTERN_OF_BAR);
		return (endDate.getTime() - beginDate.getTime()) / 1000;
	}

	/**
	 * 字符串转成日期
	 */
	public static Date stringToDateTime(String date) {
		if (StringUtils.isEmpty(date)) {
			return null;
		}
		try {
			return new SimpleDateFormat("yyyyMMddHHmmss").parse(date);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 获取当天的开始时间
	 *
	 * @return Date实例（如：2018-07-03 00:00:00）
	 */
	public static Date getTodayStarting() {
		Calendar cal = Calendar.getInstance();
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MILLISECOND, 0);
		return cal.getTime();
	}

	/**
	 * 获取当天的结束时间
	 *
	 * @return Date实例（如：2018-07-03 23:59:59）
	 */
	public static Date getTodayEnding() {
		Calendar cal = Calendar.getInstance();
		cal.set(Calendar.HOUR_OF_DAY, 23);
		cal.set(Calendar.MINUTE, 59);
		cal.set(Calendar.SECOND, 59);
		cal.set(Calendar.MILLISECOND, 0);
		return cal.getTime();
	}

	/**
	 * 获取某天的开始时间
	 *
	 * @return Date实例（如：2018-07-03 00:00:00）
	 */
	public static Date getStarting(Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MILLISECOND, 0);
		return cal.getTime();
	}

	/**
	 * 获取某天的结束时间
	 *
	 * @return Date实例（如：2018-07-03 23:59:59）
	 */
	public static Date getEnding(Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		cal.set(Calendar.HOUR_OF_DAY, 23);
		cal.set(Calendar.MINUTE, 59);
		cal.set(Calendar.SECOND, 59);
		cal.set(Calendar.MILLISECOND, 0);
		return cal.getTime();
	}

	private static boolean isEmpty(String str) {
		if (str == null || "".equals(str.trim())) {
			return true;
		}
		return false;
	}

	public static String getWeek(Date date) throws Exception {
		Calendar calendar = Calendar.getInstance();
		String[] weekDays = {"Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"};
		calendar.setTime(date);
		int w = calendar.get(Calendar.DAY_OF_WEEK) - 1;
		return weekDays[w];
	}

	/**
	 * 获取昨天的开始时间
	 */
	public static Date getYesterday() {
		Calendar cal = Calendar.getInstance();
		cal.setTime(getTodayStarting());
		cal.add(Calendar.DAY_OF_MONTH, -1);
		return cal.getTime();
	}

	/**
	 * 获取当天日期的字符串yyyyMMdd
	 *
	 * @return String实例（如：20180703）
	 */
	public static String getTodayDateString() {
		return format(new Date(), DATE_PATTERN);
	}

	/**
	 * 打印x天x时x分x秒
	 *
	 * @param time 单位秒
	 * @return String x天x时x分x秒
	 */
	public static String toTimeString(long time) {
		if (time < 60) {
			return time + "秒";
		} else if (time < 3600) {
			return (time / 60) + "分" + (time % 60) + "秒";
		} else if (time < 3600 * 24) {
			return (time / 60 / 60) + "时" + (time / 60 % 60) + "分" + (time % 60) + "秒";
		} else {
			return (time / 60 / 60 / 24) + "天" + (time / 60 / 60 % 24) + "时" + (time / 60 % 60) + "分" + (time % 60) + "秒";
		}
	}


	public static LocalDateTime toLocalDateTime(Date date){
		if(Objects.isNull(date)){
			return null;
		}
		return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
	}

	public static Date toDate(LocalDateTime localDateTime){
		if(Objects.isNull(localDateTime)){
			return null;
		}
		return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
	}

	/**
	 * 判断参数的格式是否为“yyyyMMdd”格式的合法日期字符串
	 *
	 */
	public static boolean isValidDate(String str) {
		try {
			if (str != null && !str.equals("")) {
				if (str.length() == 8) {
					// 闰年标志
					boolean isLeapYear = false;
					String year = str.substring(0, 4);
					String month = str.substring(4, 6);
					String day = str.substring(6, 8);
					int vYear = Integer.parseInt(year);
					// 判断年份是否合法
					if (vYear < 1900 || vYear > 2200) {
						return false;
					}
					// 判断是否为闰年
					if (vYear % 4 == 0 && vYear % 100 != 0 || vYear % 400 == 0) {
						isLeapYear = true;
					}
					// 判断月份
					// 1.判断月份
					if (month.startsWith("0")) {
						String units4Month = month.substring(1, 2);
						int vUnits4Month = Integer.parseInt(units4Month);
						if (vUnits4Month == 0) {
							return false;
						}
						if (vUnits4Month == 2) {
							// 获取2月的天数
							int vDays4February = Integer.parseInt(day);
							if (isLeapYear) {
								if (vDays4February > 29)
									return false;
							} else {
								if (vDays4February > 28)
									return false;
							}
						}
					} else {
						// 2.判断非0打头的月份是否合法
						int vMonth = Integer.parseInt(month);
						if (vMonth != 10 && vMonth != 11 && vMonth != 12) {
							return false;
						}
					}
					// 判断日期
					// 1.判断日期
					if (day.startsWith("0")) {
						String units4Day = day.substring(1, 2);
						int vUnits4Day = Integer.parseInt(units4Day);
						if (vUnits4Day == 0) {
							return false;
						}
					} else {
						// 2.判断非0打头的日期是否合法
						int vDay = Integer.parseInt(day);
						if (vDay < 10 || vDay > 31) {
							return false;
						}
					}
					return true;
				} else {
					return false;
				}
			} else {
				return false;
			}
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}

	/**
	 * 判断开始时间是否小于等于结束时间
	 */
	public static boolean compareStartAndEndDate(String beginTime, String endTime) {
		if (isEmpty(beginTime) || isEmpty(endTime)) {
			return false;
		}
		Date beginDate = stringToDateTime(beginTime, DATE_PATTERN);
		Date endDate = stringToDateTime(endTime, DATE_PATTERN);
		return endDate.getTime() - beginDate.getTime() >= 0 ? true : false;
	}

	/**
	 * 日期的加减运算。
	 *
	 * @see Calendar#add(int, int)
	 */
	public static Date addDate(Date date, int field, int amount) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		cal.add(field, amount);
		return cal.getTime();
	}


	/**
	 * 指定时间往前推几天
	 * @param specifiedDay
	 * @param x
	 * @return
	 */
	public static Date getDayBefore(Date specifiedDay,int x){
		Calendar c=Calendar.getInstance();
		c.setTime(specifiedDay);
		int day=c.get(Calendar.DATE);
		c.set(Calendar.DATE,day-x);
		Date time = c.getTime();
		return time;
	}

	/**
	 * 获取当前时间字符串（无符号）
	 *
	 * @return
	 */
	public static String nowNonDelimiterDateTimeStr() {
		return now().format(DATE_TIME_FORMATTER);
	}

	/**
	 * 计算年龄
	 *
	 * @param birthDay 出生日期
	 * @return Long
	 */
	public static Long calcAge(LocalDate birthDay) {
		return calcAge(birthDay, LocalDate.now());
	}

	/**
	 * 计算年龄
	 *
	 * @param birthDay 出生日期
	 * @return Long
	 */
	public static Long calcAge(LocalDateTime birthDay) {
		return calcAge(toLocalDate(birthDay), LocalDate.now());
	}

	/**
	 * 计算年龄
	 *
	 * @param birthDay  出生日期
	 * @param localDate 需要于出生日期计算的日期
	 * @return Long
	 */
	public static Long calcAge(LocalDate birthDay, LocalDate localDate) {
		if (Objects.isNull(birthDay) || Objects.isNull(localDate)) {
			return null;
		}
		return ChronoUnit.YEARS.between(birthDay, localDate);
	}

	/**
	 * 将日期转换为时间
	 */
	public static LocalDateTime toLocalDateTime(LocalDate localDate) {
		if (Objects.isNull(localDate)) {
			return null;
		}
		return LocalDateTime.of(localDate, LocalTime.MIN);
	}

	/**
	 * 将日期转换为时间
	 */
	public static LocalDate toLocalDate(LocalDateTime localDateTime) {
		if (Objects.isNull(localDateTime)) {
			return null;
		}
		return localDateTime.toLocalDate();
	}

	/**
	 * 获取两个时间中的最大值
	 */
	public static LocalDateTime max(LocalDateTime time1, LocalDateTime time2) {
		if (Objects.isNull(time1)) {
			return time2;
		}
		if (Objects.isNull(time2)) {
			return time1;
		}
		return time1.isAfter(time2) ? time1 : time2;
	}
	/**
	 * 获取当天开始时间 yyyy-MM-dd 00:00:00 格式
	 * @return
	 */
	public static Date getStartDateTime() {
		return DateUtil.getStarting(new Date());
	}
	/**
	 * 获取当天结束时间 yyyy-MM-dd 23:59:59 格式
	 * @return
	 */
	public static Date getEndDateTime() {
		return DateUtil.getEnding(new Date());
	}
	/**
	 * 获取当天开始时间 yyyy-MM-dd 00:00:00 格式
	 * @return
	 */
	public static String getStartDateTimeStr() {
		return DateUtil.format(DateUtil.getStarting(new Date()),DateUtil.DATE_TIME_PATTERN_OF_BAR);
	}
	/**
	 * 获取当天结束时间 yyyy-MM-dd 23:59:59 格式
	 * @return
	 */
	public static String getEndDateTimeStr() {
		return DateUtil.format(DateUtil.getEnding(new Date()),DateUtil.DATE_TIME_PATTERN_OF_BAR);
	}

	/**
	 * 计算两个日期之间的差值，并格式化为"多少天，多少小时"
	 * @param date1 第一个日期
	 * @param date2 第二个日期
	 * @return 格式化后的时间差字符串
	 */
	public static String getDateDifference(Date date1, Date date2) {
		// 计算时间差（毫秒）
		long difference = Math.abs(date2.getTime() - date1.getTime());
		// 转换为天和小时
		long days = (difference / (24 * 60 * 60 * 1000) ) + 1;
		long hours = (difference % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000);
		// 返回格式化结果
		if(hours == 0){
			return days + "天";
		}
		// +1 包含当天
		return days + "天" + hours + "小时";
	}




	/**
	 * 根据类型获取对应的时间范围
	 *
	 @param type
	 时间范围类型（1:今天, 2:昨天, 3:近一周）
	 *
	 @return
	 包含开始时间和结束时间的Date数组，索引0为开始时间，索引1为结束时间
	 */
	public static Date[] getTimeRange(int type) {
		DateTime now = cn.hutool.core.date.DateUtil.date();
		Date[] timeRange = new Date[2];

		switch (type) {
			case 1: // 今天
				timeRange
						[0] = cn.hutool.core.date.DateUtil.beginOfDay(now).toJdkDate();
				timeRange
						[1] = cn.hutool.core.date.DateUtil.endOfDay(now).toJdkDate();
				break;
			case 2: // 昨天
				DateTime yesterday = cn.hutool.core.date.DateUtil.yesterday();
				timeRange
						[0] = cn.hutool.core.date.DateUtil.beginOfDay(yesterday).toJdkDate();
				timeRange
						[1] = cn.hutool.core.date.DateUtil.endOfDay(yesterday).toJdkDate();
				break;
			case 3: // 近一周（包含今天）
				DateTime weekAgo = cn.hutool.core.date.DateUtil.offsetDay(now, -6); // 7天前（包含今天共7天）
				timeRange
						[0] = cn.hutool.core.date.DateUtil.beginOfDay(weekAgo).toJdkDate();
				timeRange
						[1] = cn.hutool.core.date.DateUtil.endOfDay(now).toJdkDate();
				break;
			default:
				throw new IllegalArgumentException("不支持的时间范围类型：" + type);
		}

		return timeRange;
	}

	/**
	 * 根据类型获取对应的时间范围字符串（格式：yyyy-MM-dd HH:mm:ss）
	 *
	 @param type
	 时间范围类型（1:今天, 2:昨天, 3:近一周）
	 *
	 @return
	 包含开始时间和结束时间字符串的数组，索引0为开始时间，索引1为结束时间
	 */
	public static String[] getTimeRangeStr(int type) {
		Date[] timeRange = getTimeRange(type);
		return new String[]{
				cn.hutool.core.date.DateUtil.formatDateTime(timeRange[0]),
				cn.hutool.core.date.DateUtil.formatDateTime(timeRange[1])
		};
	}


	/**
	 * 获取两个日期之间的所有日期（包含起始和结束日期）
	 * @param startTime 开始日期
	 * @param endTime 结束日期
	 * @return 日期列表（格式：yyyy-MM-dd）
	 */
	public static List<String> getDatesBetween(Date startTime, Date endTime) {
		// 将 java.util.Date 转换为 LocalDate
		LocalDate startDate = startTime.toInstant()
				.atZone(ZoneId.systemDefault())
				.toLocalDate();

		LocalDate endDate = endTime.toInstant()
				.atZone(ZoneId.systemDefault())
				.toLocalDate();
		List<String> dates = new ArrayList<>();
		LocalDate currentDate = startDate;

		// 遍历从 startDate 到 endDate 的每一天
		while (!currentDate.isAfter(endDate)) {
			dates.add(currentDate.toString()); // 默认格式：yyyy-MM-dd
			currentDate = currentDate.plusDays(1);
		}
		return dates;
	}

	/**
	 * 获取两个日期之间的所有日期对应的星期几（包含起始和结束日期）
	 * @param startTime 开始日期
	 * @param endTime 结束日期
	 * @return 星期几列表（例如：星期四，星期五，星期六）
	 */
	public static List<String> getWeekdaysBetween(Date startTime, Date endTime) {
		// 将 java.util.Date 转换为 LocalDate
		LocalDate startDate = startTime.toInstant()
				.atZone(ZoneId.systemDefault())
				.toLocalDate();

		LocalDate endDate = endTime.toInstant()
				.atZone(ZoneId.systemDefault())
				.toLocalDate();

		List<String> weekdays = new ArrayList<>();
		LocalDate currentDate = startDate;
		// 中文星期几映射
		String[] chineseWeekdays = {"星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"};
		// 遍历日期并获取星期几
		while (!currentDate.isAfter(endDate)) {
			// 获取星期几（1=Monday, 7=Sunday）
			DayOfWeek dayOfWeek = currentDate.getDayOfWeek();
			// 转换为中文表示
			weekdays.add(chineseWeekdays[dayOfWeek.getValue() - 1]);
			currentDate = currentDate.plusDays(1);
		}
		return weekdays;
	}

}
