conf:
  server:
    port:
      ihc: 9200
  snowflake:
    worker-id: 1 		#全局唯一guid生成器终端ID,最大值为31，最小值为1
    datacenter-id: 2 	#全局唯一guid生成器数据中心ID,最大值为31，最小值为1
  system-mark: ihc
  matchers:
    ignores: /doc.html/**,/swagger/**,/rpc-api/**,/v2/upload,/bsp/dic/**
  debug: false
  datasource:
    druid:
      log-slow-sql: true
      slow-sql-millis: 100
    dynamic:
      druid:
        initial-size: 1
        min-idle: 1 		# 最小连接池数量
        max-active: 20 		# 最大连接池数量
        max-wait: 600000 	# 配置获取连接等待超时的时间，单位：毫秒
      master:
        url: jdbc:postgresql://*************:5432/rs_v1?useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true&nullCatalogMeansCurrent=true&rewriteBatchedStatements=true # MySQL Connector/J 8.X 连接的示例
        username: postgres
        password: Go@123456
      bsp:
        url: ************************************************************************************************************************************************************************** # MySQL Connector/J 8.X 连接的示例
        username: root
        password: sundun_bsp
  mongodb:
    uri: mongodb://*************:27111/bsp
    hosts:
  redis:
    host: *************
    port: 6399
    database: 3
    password: redisbsp
    timeout: 6000  # 连接超时时长（毫秒）
    max-redirects: 3
    lettuce:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接

---
conf:
  nacos:
    enabled: true
    ip: *************
    port: 8848
    username: nacos
    password: nacos@gxx
    namespace: rs
    group: DEFAULT_GROUP
---
conf:
  dromara:
    x-file-storage:
      enable-storage: true
      access-key: admin
      secret-key: admin123456
      end-point: http://*************:9010
      bucket-name: ihc
      domain: http://*************:9010/ihc/
      base-path:

---
conf:
  bsp:
    token:
      url: http://*************:1910/oauth/token
---
conf:
  xxl:
    enabled: false
    admin:
      addresses: http://*************:8080/xxl-job-admin
      username: admin
      password: xxlbsp
    executor:
      ip: ************
      port: 9202
      logPath: D:\\logs\\xxl-job\\${spring.application.name}


# 使用Ribbon配置（默认情况下Feign使用Ribbon作为负载均衡器）
ribbon:
  ConnectTimeout: 5000     # 连接超时时间（毫秒）
  ReadTimeout: 60000       # 读取超时时间（毫秒，设置为1分钟）

# 或者直接使用Feign的配置（优先级更高）
feign:
  client:
    config:
      default:  # 全局配置
        connectTimeout: 5000
        readTimeout: 60000