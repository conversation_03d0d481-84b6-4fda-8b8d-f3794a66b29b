<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rs.module.ihc.dao.ipm.OutpatientDao">
    <select id="getTodayStat" resultType="com.rs.module.ihc.controller.admin.ipm.vo.InternalMedicalOutpatientTodayStatVO">
        SELECT count(case when o.status = 0 then 1 end) todo_num,
        count(case when o.status != 0 and mp.prescribe_time between #{start} and #{end} then 1 end) done_num
        FROM ihcs_internal_medical_outpatient o
        left join ihcs_internal_medical_prescribe mp on mp.business_id = o.id and mp.type = 3
        <where>
            <if test="prisonId != null and prisonId != ''">
                o.prison_id = #{prisonId}
            </if>
        </where>
    </select>
</mapper>
