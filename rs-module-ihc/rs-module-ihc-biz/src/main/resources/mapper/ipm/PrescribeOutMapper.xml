<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rs.module.ihc.dao.ipm.PrescribeOutDao">

    <select id="getPrescribeOutById" resultType="com.rs.module.ihc.controller.admin.ipm.vo.PrescribeOutRespVO">
        SELECT
            t1.*,t2.xm jgryxm
        FROM
            ihc_ipm_prescribe_out t1
        LEFT JOIN vw_acp_pm_prisoner t2 ON t1.jgrybm = t2.jgrybm
        where t1.id = #{id}
    </select>

</mapper>
