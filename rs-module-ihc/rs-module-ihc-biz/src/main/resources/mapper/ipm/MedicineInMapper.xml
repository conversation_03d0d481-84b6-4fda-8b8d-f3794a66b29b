<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rs.module.ihc.dao.pm.MedicineInDao">


    <select id="getExistExpireBatchMedicineId" resultType="java.lang.String">
        select distinct t1.medicine_id
        from ihc_pm_medicine_in t1
                 inner join ihc_pm_medicine t2 on t1.medicine_id = t2.id
        where t1.inventory_num > 0
          and t2.has_expire_date_batch = 0
          and t1.expire_date &lt; #{expireDate}
        <if test="medicineIdList != null and medicineIdList.size() != 0">
            and t1.medicine_id in
            <foreach collection="medicineIdList" item="medicineId" open="(" separator="," close=")">
                #{medicineId}
            </foreach>
        </if>
    </select>

    <select id="selectFirstNoExpireMedicineBatch" resultType="com.rs.module.ihc.entity.pm.MedicineInDO">
        select t1.id,
               t1.medicine_id,
               t1.in_num,
               t1.batch_code,
               t1.in_date,
               t1.expire_date,
               t1.inventory_num
        from  ihc_pm_medicine_in t1
            inner join (select min(id) as id
                        from ihc_pm_medicine_in
        where expire_date &gt;= #{expireDate}
          and inventory_num > 0
          and medicine_id in
        <foreach collection="medicineIdList" item="medicineId" open="(" separator="," close=")">
            #{medicineId}
        </foreach>
        group by medicine_id) t2 on t1.id = t2.id
    </select>

    <select id="getMaxBatchCode" resultType="com.rs.module.ihc.controller.admin.pm.vo.GetMaxBatchCodeVO">
        select max(batch_code) as maxBatchCode,
        in_date         as inDate
        from ihc_pm_medicine_in
        where in_date in
        <foreach collection="inDateList" item="inDate" open="(" separator="," close=")">
            #{inDate}
        </foreach>
        group by in_date
    </select>



</mapper>
