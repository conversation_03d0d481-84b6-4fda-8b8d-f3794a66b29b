<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rs.module.ihc.dao.ipm.PrescribeOutMedicineDao">

    <select id="selectListByOutId" resultType="com.rs.module.ihc.entity.ipm.PrescribeOutMedicineDO">
        SELECT
            t1.*,t2.medicine_name "medicineName",t2.specs,t2.measurement_unit "measurementUnit",
            t2.min_measurement_unit "minMeasurementUnit",t2.unit_conversion_ratio "unitConversionRatio"
        FROM
            ihc_ipm_prescribe_out_medicine t1
        LEFT JOIN ihc_pm_medicine t2 ON t1.medicine_id = t2.id
        where t1.out_id = #{outId}
    </select>

</mapper>
