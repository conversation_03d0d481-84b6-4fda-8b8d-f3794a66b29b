<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rs.module.ihc.dao.ipm.PrescribeDao">
    <select id="listPrisonerOutPrescribe" resultType="java.lang.String">
        select t1.id
        from ihc_ipm_prescribe t1
                 inner join vw_acp_pm_prisoner_list t2 on t1.jgrybm = t2.jgrybm
        where t2.ryzt='11' and t1.prescribe_status not in
        <foreach collection="filterPrescribeStatusList" item="prescribeStatus" open="(" separator="," close=")">
            #{prescribeStatus}
        </foreach>
    </select>

    <select id="getPrescribeList" resultType="com.rs.module.ihc.controller.admin.ipm.vo.PrescribeRespVO">
        SELECT
            t1.id,
            t1.prescribe_type,
            t1.doctor_advice_type,
            t1.primary_diagnosis,
            t1.prescribe_status,
            t1.suggestion,
            t1.doctor_advice,
            t1.jgrybm,
            t1.dispense_num,
            t1.last_dispense_time,
            t1.update_time,
            t1.prescribe_num,
            t2.xm AS supervised_user_name,
            t2.jsh AS room_id,
            t2.dabh,
            t3.room_name,
            t4.medicine_name,
            COALESCE(t5.dispenseNum, 0) dispenseNum,
            COALESCE(t5.todayDispenseNum, 0) todayDispenseNum
        FROM
            ihc_ipm_prescribe t1
                LEFT JOIN vw_acp_pm_prisoner_list t2 ON t1.jgrybm = t2.jgrybm
                LEFT JOIN acp_pm_area_prison_room t3 ON t2.jsh = t3.id
                LEFT JOIN (
                SELECT
                    tt0.mlh,
                    STRING_AGG(tt1.medicine_name, ',') medicine_name
                FROM
                    ihc_ipm_prescribe_medicine tt0
                        INNER JOIN ihc_pm_medicine tt1 ON tt0.medicine_id = tt1.id
                GROUP BY
                    tt0.mlh
            ) t4 ON t1.id = t4.mlh
                LEFT JOIN (
                SELECT
                    prescribe_id,
                    COUNT(1) dispenseNum,
                    COUNT(CASE WHEN add_time BETWEEN CURRENT_DATE::timestamp AND (CURRENT_DATE + INTERVAL '1 day')::timestamp - INTERVAL '1 second' THEN 1 END) todayDispenseNum
                FROM
                    ihc_ipm_prescribe_execute
                WHERE is_del = '0'
                GROUP BY
                    prescribe_id
            ) t5 ON t1.id = t5.prescribe_id
        <where>
            t1.is_del = '0'
            <if test="roomId != null and roomId != ''">
                AND t2.jsh = #{roomId}
            </if>
        </where>

        ORDER BY t1.update_time desc

    </select>

    <select id="getPrescribeListByJgrybm" resultType="com.rs.module.ihc.controller.admin.ipm.vo.PrescribeRespVO">
        select t1.*
        from ihc_ipm_prescribe t1
                 inner join vw_acp_pm_prisoner_list t2 on t1.jgrybm = t2.jgrybm
        <where>
            t1.is_del = '0'
            <if test="jgrybm != null and jgrybm != ''">
                AND t1.jgrybm = #{jgrybm}
            </if>
            <if test="doctorAdviceType != null and doctorAdviceType != ''">
                AND t1.doctor_advice_type = #{doctorAdviceType}
            </if>
        </where>
        order by t1.update_time desc NULLS LAST
    </select>
</mapper>
