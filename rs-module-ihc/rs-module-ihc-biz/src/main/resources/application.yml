server:
  port: ${conf.server.port.ihc}
  max-http-header-size: 102400

# 日志
logging:
  file:
    name: ${user.home}/logs/${spring.application.name}.log # 日志文件名，全路径

# spring
spring:
  application:
    name: ihc-server
  profiles:
    active: conf,dev,sdk,as
  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
    allow-bean-definition-overriding: true # 允许 Bean 覆盖，例如说 Feign 等会存在重复定义的服务

  # Spring Data Redis 配置
  data:
    redis:
      repositories:
        enabled: false # 项目未使用到 Spring Data Redis 的 Repository，所以直接禁用，保证启动速度

  # Servlet 配置
  servlet:
    # 文件上传相关配置项
    multipart:
      max-file-size: 1600MB # 单个文件大小
      max-request-size: 3200MB # 设置总上传的文件大小

  # Jackson 配置项
  jackson:
    serialization:
      write-dates-as-timestamps: true # 设置 LocalDateTime 的格式，使用时间戳
      write-date-timestamps-as-nanoseconds: false # 设置不使用 nanoseconds 的格式。例如说 1611460870.401，而是直接 1611460870401
      write-durations-as-timestamps: true # 设置 Duration 的格式，使用时间戳
      fail-on-empty-beans: false # 允许序列化无属性的 Bean

  # 放行的路由
  matchers:
    ignores: ${conf.matchers.ignores}

# 接口文档配置
springdoc:
  api-docs:
    enabled: true # 1. 是否开启 Swagger 接文档的元数据
    path: /v3/api-docs
  swagger-ui:
    enabled: true # 2.1 是否开启 Swagger 文档的官方 UI 界面
    path: /swagger-ui
  default-flat-param-object: true # 参见 https://doc.xiaominfo.com/docs/faq/v4/knife4j-parameterobject-flat-param 文档

# MyBatis Plus 的配置项
mybatis-plus:
  mapper-locations:
  - classpath*:mapper/**/*.xml
  configuration:
    map-underscore-to-camel-case: true # 虽然默认为 true ，但是还是显示去指定下。
  global-config:
    db-config:
      id-type: NONE # “智能”模式，基于 IdTypeEnvironmentPostProcessor + 数据源的类型，自动适配成 AUTO、INPUT 模式。
      #      id-type: AUTO # 自增 ID，适合 MySQL 等直接自增的数据库
      #      id-type: INPUT # 用户输入 ID，适合 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库
      #      id-type: ASSIGN_ID # 分配 ID，默认使用雪花算法。注意，Oracle、PostgreSQL、Kingbase、DB2、H2 数据库时，需要去除实体类上的 @KeySequence 注解
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
    banner: false # 关闭控制台的 Banner 打印
  type-aliases-package: ${rs.info.base-package}.dao
  encryptor:
    password: XDV71a+xqStEA3WH # 加解密的秘钥，可使用 https://www.imaegoo.com/2020/aes-key-generator/ 网站生成


##########################   bsp相关配置  ##########################
bsp:
  snowflake:
    worker-id: ${conf.snowflake.worker-id}
    datacenter-id: ${conf.snowflake.datacenter-id}
  data-permission: # 数据权限配置
    enable: true # 是否开启数据权限
  token:
    url: ${conf.bsp.token.url}
system-mark: ${conf.system-mark}


########################## 医疗系统相关配置 ##########################
rs:
  info:
    version: 1.0.0
    base-package: com.rs.module.ihc
  web:
    admin-ui:
      url: http://dashboard.rs # Admin 管理后台
  swagger:
    title: 医疗管理系统
    description: 提供医疗管理系统的所有功能
    version: ${rs.info.version}
    base-package: com.rs.module.ihc
debug: ${conf.debug}
---
spring:
  # Spring Cloud Nacos 配置
  cloud:
    nacos:
      discovery: # 【配置中心】配置项
        enabled: ${conf.nacos.enabled}
        server-addr: ${conf.nacos.ip}:${conf.nacos.port} # Nacos 服务器地址
        username: ${conf.nacos.username}
        password: ${conf.nacos.password}
        namespace: ${conf.nacos.namespace}
        group: ${conf.nacos.group}
        metadata:
          version: 1.0.0 # 服务实例的版本号，可用于灰度发布
---
###文件存储配置
dromara:
  x-file-storage: #文件存储配置
    default-platform: minio-1 #默认使用的存储平台
    thumbnail-suffix: ".min.jpg" #缩略图后缀，例如【.min.jpg】【.png】
    #对应平台的配置写在这里，注意缩进要对齐
    minio:
      - platform: minio-1 # 存储平台标识
        enable-storage: true  # 启用存储
        access-key: ${conf.dromara.x-file-storage.access-key}
        secret-key: ${conf.dromara.x-file-storage.secret-key}
        end-point: ${conf.dromara.x-file-storage.end-point}
        bucket-name: ${conf.dromara.x-file-storage.bucket-name}
        domain:  ${conf.dromara.x-file-storage.domain}
        base-path: # 基础路径
easy-trans:
  #启用redis缓存 如果不用redis请设置为false
  is-enable-redis: true
  #启用全局翻译(拦截所有responseBody进行自动翻译)，如果对于性能要求很高可关闭此配置
  is-enable-global: true
  #启用平铺模式
  is-enable-tile: true
---
bean-searcher:
  params:
    sort: bsort
    order: border
    pagination:
      # 起始页，不配置默认为0，这里配置为1，是为了兼容element UI的分页组件
      start: 1
      default-size: 1000
      max-allowed-size: 1000000
  sql:
    dialect: Mysql
---
spring:
  jpa:
    # 数据库类型
    database: mysql
    #打印SQL
    show-sql: true
    hibernate:
      ddl-auto: update  #第一次启动创建表，之后修改为update
---
xxl:
  job:
    enabled: ${conf.xxl.enabled}
    admin:
      addresses: ${conf.xxl.admin.addresses}
      username: ${conf.xxl.admin.username}
      password: ${conf.xxl.admin.password}
    executor:
      appName: ${spring.application.name}
      ip: ${conf.xxl.executor.ip}
      port: ${conf.xxl.executor.port}
      logPath: ${conf.xxl.executor.logPath}
    accessToken: default_token
