package com.rs.module.ihc.controller.admin.hr;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.ihc.controller.admin.hr.vo.*;
import com.rs.module.ihc.entity.hr.PrintdocConfigDO;
import com.rs.module.ihc.service.hr.PrintdocConfigService;

@Api(tags = "管理后台 - 健康档案-打印文书配置")
@RestController
@RequestMapping("/ihc/hr/printdoc-config")
@Validated
public class PrintdocConfigController {

    @Resource
    private PrintdocConfigService printdocConfigService;

    @PostMapping("/create")
    @ApiOperation(value = "创建健康档案-打印文书配置")
    @LogRecordAnnotation(bizModule = "ihc:printdocConfig:create", operateType = LogOperateType.CREATE, title = "创建健康档案-打印文书配置",
    success = "创建健康档案-打印文书配置成功", fail = "创建健康档案-打印文书配置失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createPrintdocConfig(@Valid @RequestBody PrintdocConfigSaveReqVO createReqVO) {
        return success(printdocConfigService.createPrintdocConfig(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新健康档案-打印文书配置")
    @LogRecordAnnotation(bizModule = "ihc:printdocConfig:update", operateType = LogOperateType.UPDATE, title = "更新健康档案-打印文书配置",
    success = "更新健康档案-打印文书配置成功", fail = "更新健康档案-打印文书配置失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updatePrintdocConfig(@Valid @RequestBody PrintdocConfigSaveReqVO updateReqVO) {
        printdocConfigService.updatePrintdocConfig(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除健康档案-打印文书配置")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:printdocConfig:delete", operateType = LogOperateType.DELETE, title = "删除健康档案-打印文书配置",
    success = "删除健康档案-打印文书配置成功", fail = "删除健康档案-打印文书配置失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deletePrintdocConfig(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           printdocConfigService.deletePrintdocConfig(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得健康档案-打印文书配置")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:printdocConfig:get", operateType = LogOperateType.QUERY, title = "获取健康档案-打印文书配置", bizNo = "{{#id}}", success = "获取健康档案-打印文书配置成功", fail = "获取健康档案-打印文书配置失败", extraInfo = "{{#id}}")
    public CommonResult<PrintdocConfigRespVO> getPrintdocConfig(@RequestParam("id") String id) {
        PrintdocConfigDO printdocConfig = printdocConfigService.getPrintdocConfig(id);
        return success(BeanUtils.toBean(printdocConfig, PrintdocConfigRespVO.class));
    }

    @GetMapping("/page")
    @ApiOperation(value = "获得健康档案-打印文书配置分页")
    @LogRecordAnnotation(bizModule = "ihc:printdocConfig:page", operateType = LogOperateType.QUERY, title = "获得健康档案-打印文书配置分页",
    success = "获得健康档案-打印文书配置分页成功", fail = "获得健康档案-打印文书配置分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<PrintdocConfigRespVO>> getPrintdocConfigPage(@Valid PrintdocConfigPageReqVO pageReqVO) {
        PageResult<PrintdocConfigDO> pageResult = printdocConfigService.getPrintdocConfigPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PrintdocConfigRespVO.class));
    }

    @GetMapping("/list")
    @ApiOperation(value = "获得健康档案-打印文书配置列表")
    @LogRecordAnnotation(bizModule = "ihc:printdocConfig:list", operateType = LogOperateType.QUERY, title = "获得健康档案-打印文书配置列表",
    success = "获得健康档案-打印文书配置列表成功", fail = "获得健康档案-打印文书配置列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<PrintdocConfigRespVO>> getPrintdocConfigList(@Valid PrintdocConfigListReqVO listReqVO) {
    List<PrintdocConfigDO> list = printdocConfigService.getPrintdocConfigList(listReqVO);
        return success(BeanUtils.toBean(list, PrintdocConfigRespVO.class));
    }

}
