package com.rs.module.ihc.controller.admin.pm;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicineListReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicinePageReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicineRespVO;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicineSaveReqVO;
import com.rs.module.ihc.entity.pm.PharmacyMedicineDO;
import com.rs.module.ihc.service.pm.PharmacyMedicineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "大药房管理-药品信息")
@RestController
@RequestMapping("/ihc/pm/pharmacyMedicine")
@Validated
public class PharmacyMedicineController {

    @Resource
    private PharmacyMedicineService pharmacyMedicineService;

    @PostMapping("/create")
    @ApiOperation(value = "创建大药房管理-药品信息")
    @LogRecordAnnotation(bizModule = "ihc:pharmacyMedicine:create", operateType = LogOperateType.CREATE, title = "创建大药房管理-药房信息",
            success = "创建大药房管理-药房信息成功", fail = "创建药房管理-药房信息失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createPharmacyMedicine(@Valid @RequestBody PharmacyMedicineSaveReqVO createReqVO) {
        return success(pharmacyMedicineService.createPharmacyMedicine(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新大药房管理-药品信息")
    @LogRecordAnnotation(bizModule = "ihc:pharmacyMedicine:update", operateType = LogOperateType.UPDATE, title = "更新大药房管理-药房信息",
            success = "更新大药房管理-药房信息成功", fail = "更新大药房管理-药房信息失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updatePharmacyMedicine(@Valid @RequestBody PharmacyMedicineSaveReqVO updateReqVO) {
        pharmacyMedicineService.updatePharmacyMedicine(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除大药房管理-药品信息")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:pharmacyMedicine:delete", operateType = LogOperateType.DELETE, title = "删除大药房管理-药房信息",
            success = "删除大药房管理-药房信息成功", fail = "删除大药房管理-药房信息失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deletePharmacyMedicine(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           pharmacyMedicineService.deletePharmacyMedicine(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得大药房管理-药品信息")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:pharmacyMedicine:get", operateType = LogOperateType.QUERY,
            title = "获得大药房管理-药房信息", bizNo = "{{#id}}", success = "获得大药房管理-药房信息成功", fail = "获得大药房管理-药房信息失败", extraInfo = "{{#id}}")
    public CommonResult<PharmacyMedicineRespVO> getPharmacyMedicine(@RequestParam("id") String id) {
        PharmacyMedicineDO pharmacyMedicine = pharmacyMedicineService.getPharmacyMedicine(id);
        return success(BeanUtils.toBean(pharmacyMedicine, PharmacyMedicineRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得大药房管理-药品信息分页")
    @LogRecordAnnotation(bizModule = "ihc:pharmacyMedicine:page", operateType = LogOperateType.QUERY, title = "获得大药房管理-药房信息分页",
            success = "获得大药房管理-药房信息分页成功", fail = "获得大药房管理-药房信息分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<PharmacyMedicineRespVO>> getPharmacyMedicinePage(@Valid @RequestBody PharmacyMedicinePageReqVO pageReqVO) {
        PageResult<PharmacyMedicineDO> pageResult = pharmacyMedicineService.getPharmacyMedicinePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PharmacyMedicineRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得大药房管理-药品信息列表")
    @LogRecordAnnotation(bizModule = "ihc:pharmacyMedicine:list", operateType = LogOperateType.QUERY, title = "获得药房管理-药房信息列表",
            success = "获得药房管理-药房信息列表成功", fail = "获得药房管理-药房信息列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<PharmacyMedicineRespVO>> getPharmacyMedicineList(@Valid @RequestBody PharmacyMedicineListReqVO listReqVO) {
        List<PharmacyMedicineDO> list = pharmacyMedicineService.getPharmacyMedicineList(listReqVO);
        return success(BeanUtils.toBean(list, PharmacyMedicineRespVO.class));
    }
}
