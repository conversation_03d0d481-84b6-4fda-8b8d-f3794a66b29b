package com.rs.module.ihc.controller.admin.zb;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.base.controller.admin.zh.vo.staffduty.StaffDutyTemplateRespVO;
import com.rs.module.base.controller.admin.zh.vo.staffduty.StaffDutyTemplateSaveReqVO;
import com.rs.module.base.enums.StaffDutyTypeEnum;
import com.rs.module.base.service.zh.staffduty.StaffDutyTemplateService;
import com.rs.module.base.vo.DeleteRequestBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Api(value = "值班管理-模板管理", tags = "值班管理-模板管理")
@Slf4j
@RequestMapping("/ihc/zb/staffDutyTemplate")
@RestController
@Validated
public class StaffDutyTemplateController {

    @Autowired
    private StaffDutyTemplateService staffDutyTemplateService;

    @ApiOperation(value = "查看模板")
    @GetMapping(value = "/info")
    public CommonResult<StaffDutyTemplateRespVO> info(@RequestParam(value = "id") String id) {
        return CommonResult.success(staffDutyTemplateService.info(id));
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "/add")
    public CommonResult<String> add(@RequestBody StaffDutyTemplateSaveReqVO staffDutyTemplateSaveReqVO) {
        Integer result = staffDutyTemplateService.add(staffDutyTemplateSaveReqVO, StaffDutyTypeEnum.DOCTOR.getCode());
        if (result > 0) {
            return CommonResult.success("新增成功");
        } else if (result == -1) {
            return CommonResult.error("模板名字已存在");
        } else {
            return CommonResult.error("新增失败");
        }
    }

    @ApiOperation(value = "更新")
    @PostMapping(value = "/update")
    public CommonResult<String> update(@RequestBody StaffDutyTemplateSaveReqVO staffDutyTemplateSaveReqVO) {
        Integer result = staffDutyTemplateService.updateInfo(staffDutyTemplateSaveReqVO, StaffDutyTypeEnum.DOCTOR.getCode());
        if (result > 0) {
            return CommonResult.success("更新成功");
        } else if (result == -1) {
            return CommonResult.error("模板名字已存在");
        } else {
            return CommonResult.error("更新失败");
        }
    }

    @ApiOperation(value = "模板启用/停用")
    @PostMapping(value = "/tempOpen")
    @ApiImplicitParam(paramType = "query", name = "status", value = "0-启用 1-停用", dataType = "int")
    public CommonResult<String> tempOpen(@RequestParam(value = "id") String id,
                                         @RequestParam(value = "status") Integer status) {
        Integer result = staffDutyTemplateService.tempOpen(id, status, StaffDutyTypeEnum.DOCTOR.getCode());
        if (result > 0) {
            return CommonResult.success("编辑成功");
        } else if (result == -1) {
            return CommonResult.error("已经有正在启动的模板");
        } else {
            return CommonResult.error("编辑失败");
        }
    }

    @ApiOperation(value = "模板启用校验")
    @PostMapping(value = "/tempOpenCheck")
    @ApiImplicitParam(paramType = "query", name = "id", value = "模板id", dataType = "String")
    public CommonResult<Boolean> tempOpenCheck(@RequestParam(value = "id") String id) {
        Boolean result = staffDutyTemplateService.tempOpenCheck(id);
        return CommonResult.success(result);

    }
    @ApiOperation(value = "删除")
    @PostMapping(value = "/delete")
    public CommonResult<String> delete(@RequestBody @Validated DeleteRequestBody form) {
        Integer result = staffDutyTemplateService.delete(form.getIds());
        if (result > 0) {
            return CommonResult.success("删除成功");
        } else if (result == -1) {
            return CommonResult.error("模板处于非停用状态 无法删除");
        } else {
            return CommonResult.error("删除失败");
        }
    }

}
