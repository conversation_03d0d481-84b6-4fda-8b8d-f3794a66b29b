package com.rs.module.ihc.controller.admin.bd;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.bd.vo.PsychiatricMgrRespVO;
import com.rs.module.ihc.controller.admin.bd.vo.PsychiatricMgrSaveReqVO;
import com.rs.module.ihc.entity.bd.PsychiatricMgrDO;
import com.rs.module.ihc.service.bd.PsychiatricMgrService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "精神病异常管理")
@RestController
@RequestMapping("/ihc/bd/psychiatricMgr")
@Validated
public class PsychiatricMgrController {

    @Resource
    private PsychiatricMgrService psychiatricMgrService;

    @PostMapping("/create")
    @ApiOperation(value = "创建精神病异常管理")
    @LogRecordAnnotation(bizModule = "ihc:bd:psychiatricMgr-create", operateType = LogOperateType.CREATE, title = "创建精神病异常管理",
            success = "创建精神病异常管理-成功", fail = "创建精神病异常管理-错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createPsychiatricMgr(@Valid @RequestBody PsychiatricMgrSaveReqVO createReqVO) {
        return success(psychiatricMgrService.createPsychiatricMgr(createReqVO));
    }

    @PostMapping("/releasePerson")
    @ApiOperation(value = "精神病异常管理-解除人员列管状态")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:bd:psychiatricMgr-releasePerson", operateType = LogOperateType.QUERY, title = "获得精神病异常管理",
            success = "获得精神病异常管理-成功", fail = "获得精神病异常管理-错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    public CommonResult<Boolean> releasePerson(@RequestParam("id") String id) {
        psychiatricMgrService.releasePerson(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得精神病异常管理")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:bd:psychiatricMgr-get", operateType = LogOperateType.QUERY, title = "获得精神病异常管理",
            success = "获得精神病异常管理-成功", fail = "获得精神病异常管理-错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    public CommonResult<PsychiatricMgrRespVO> getPsychiatricMgr(@RequestParam("id") String id) {
        PsychiatricMgrDO psychiatricMgr = psychiatricMgrService.getPsychiatricMgr(id);
        return success(BeanUtils.toBean(psychiatricMgr, PsychiatricMgrRespVO.class));
    }

}
