package com.rs.module.ihc.controller.admin.ipm;

import cn.hutool.db.Db;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.framework.mybatis.config.GlobalConstant;
import com.rs.framework.mybatis.util.DatasourceUtil;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.dao.ipm.jpa.OutpatientChecklistJpaDao;
import com.rs.module.ihc.entity.ipm.OutpatientChecklistCategoryDO;
import com.rs.module.ihc.entity.ipm.OutpatientChecklistDO;
import com.rs.module.ihc.service.ipm.OutpatientChecklistService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.sql.DataSource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 所内就医-所内门诊-检查单")
@RestController
@RequestMapping("/ihc/ipm/outpatientChecklist")
@Validated
@Log4j2
public class OutpatientChecklistController {

    @Resource
    private OutpatientChecklistService outpatientChecklistService;

    @Resource
    private OutpatientChecklistJpaDao outpatientChecklistJpaDao;

    @PostMapping("/create")
    @ApiOperation(value = "创建所内就医-所内门诊-检查单")
    @LogRecordAnnotation(bizModule = "ihc:outpatientChecklist:create", operateType = LogOperateType.CREATE, title = "创建所内就医-所内门诊-检查单",
            success = "创建所内就医-所内门诊-检查单成功", fail = "创建所内就医-所内门诊-检查单失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createOutpatientChecklist(@Valid @RequestBody OutpatientChecklistSaveReqVO createReqVO) {
        return success(outpatientChecklistService.createOutpatientChecklist(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新所内就医-所内门诊-检查单")
    @LogRecordAnnotation(bizModule = "ihc:outpatientChecklist:update", operateType = LogOperateType.UPDATE, title = "更新所内就医-所内门诊-检查单",
            success = "更新所内就医-所内门诊-检查单成功", fail = "更新所内就医-所内门诊-检查单失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateOutpatientChecklist(@Valid @RequestBody OutpatientChecklistSaveReqVO updateReqVO) {
        outpatientChecklistService.updateOutpatientChecklist(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除所内就医-所内门诊-检查单")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:outpatientChecklist:delete", operateType = LogOperateType.DELETE, title = "删除所内就医-所内门诊-检查单",
            success = "删除所内就医-所内门诊-检查单成功", fail = "删除所内就医-所内门诊-检查单失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteOutpatientChecklist(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            outpatientChecklistService.deleteOutpatientChecklist(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得所内就医-所内门诊-检查单")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:outpatientChecklist:get", operateType = LogOperateType.QUERY, title = "获取所内就医-所内门诊-检查单", bizNo = "{{#id}}", success = "获取所内就医-所内门诊-检查单成功", fail = "获取所内就医-所内门诊-检查单失败", extraInfo = "{{#id}}")
    public CommonResult<OutpatientChecklistRespVO> getOutpatientChecklist(@RequestParam("id") String id) {
        OutpatientChecklistDO outpatientChecklist = outpatientChecklistService.getOutpatientChecklist(id);
        return success(BeanUtils.toBean(outpatientChecklist, OutpatientChecklistRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得所内就医-所内门诊-检查单分页")
    @LogRecordAnnotation(bizModule = "ihc:outpatientChecklist:page", operateType = LogOperateType.QUERY, title = "获得所内就医-所内门诊-检查单分页",
            success = "获得所内就医-所内门诊-检查单分页成功", fail = "获得所内就医-所内门诊-检查单分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<OutpatientChecklistRespVO>> getOutpatientChecklistPage(@Valid @RequestBody OutpatientChecklistPageReqVO pageReqVO) {
        PageResult<OutpatientChecklistDO> pageResult = outpatientChecklistService.getOutpatientChecklistPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, OutpatientChecklistRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得所内就医-所内门诊-检查单列表")
    @LogRecordAnnotation(bizModule = "ihc:outpatientChecklist:list", operateType = LogOperateType.QUERY, title = "获得所内就医-所内门诊-检查单列表",
            success = "获得所内就医-所内门诊-检查单列表成功", fail = "获得所内就医-所内门诊-检查单列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<OutpatientChecklistRespVO>> getOutpatientChecklistList(@Valid @RequestBody OutpatientChecklistListReqVO listReqVO) {
        List<OutpatientChecklistDO> list = outpatientChecklistService.getOutpatientChecklistList(listReqVO);
        return success(BeanUtils.toBean(list, OutpatientChecklistRespVO.class));
    }

    // ==================== 子表（所内就医-所内门诊-检查单-检查种类） ====================

    @GetMapping("/outpatient-checklist-category/list-by-checklist-id")
    @ApiOperation(value = "获得所内就医-所内门诊-检查单-检查种类列表")
    @ApiImplicitParam(name = "checklistId", value = "检查单id，对应ihc_ipm_outpatient_checklist.id")
    public CommonResult<List<OutpatientChecklistCategoryDO>> getOutpatientChecklistCategoryListByChecklistId(@RequestParam("checklistId") String checklistId) {
        return success(outpatientChecklistService.getOutpatientChecklistCategoryListByChecklistId(checklistId));
    }


    @GetMapping("/outpatient-checklist-category/dic")
    @ApiOperation(value = "获得所内就医-所内门诊-检查单-检查种类列表-字典")
    public CommonResult<List<OutpatientChecklistCategoryDicRespVO>> getOutpatientChecklistCategoryDic() {
        DataSource ds = DatasourceUtil.getDataSource(GlobalConstant.BSP_DATASOURCE_KEY);
        Db use = Db.use(ds);
        String sql = "select t.id, t.NAME as checklistCategoryName,t.CODE as checklistCategory from ops_dic_code t where PARENT_ID = '0' and dic_name = 'ZD_OUTPATIENT_CHECKLIST_CATEGORY'";
        List<OutpatientChecklistCategoryDicRespVO> query = null;
        try {
            query = use.query(sql, OutpatientChecklistCategoryDicRespVO.class);
        } catch (Exception e) {
            log.error("获得所内就医-所内门诊-检查单-检查种类列表-字典", e);
        }
        return success(query);
    }
    @ApiOperation(value = "登记检查单")
    @PostMapping("/registerCheckList")
    public CommonResult registerCheckList(@RequestBody InternalMedicalOutpatientChecklistRegisterVO vo) {
        outpatientChecklistService.registerCheckList(vo);
        return CommonResult.success();
    }

    @ApiOperation(value = "作废检查单")
    @ApiImplicitParam(name = "id", value = "检查单id", dataType = "String", paramType = "query", required = true)
    @GetMapping("/nullifyCheckListById")
    public CommonResult nullifyCheckListById(@RequestParam String id) {
        outpatientChecklistService.nullifyCheckListById(id);
        return CommonResult.success();
    }

}
