package com.rs.module.ihc.controller.admin.pm;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.handler.ChainBuilder;
import com.rs.framework.common.handler.Handler;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.entity.pm.MedicineInDO;
import com.rs.module.ihc.handler.pm.medicine.in.SaveHandler;
import com.rs.module.ihc.handler.pm.medicine.in.ValidateHandler;
import com.rs.module.ihc.listener.pm.MedicineInImportListener;
import com.rs.module.ihc.service.pm.MedicineInService;
import com.rs.module.ihc.service.pm.MedicineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 药房管理-药品入库批次")
@RestController
@RequestMapping("/ihc/pm/medicine-in")
@Validated
public class MedicineInController {

    @Resource
    private MedicineInService medicineInService;
    @Resource
    private MedicineService medicineService;

    @Autowired
    private ChainBuilder<MedicineInImportListVO> chainBuilder;
    @Autowired
    private SaveHandler saveHandler;
    @Autowired
    private ValidateHandler validateHandler;

    @PostMapping("/create")
    @ApiOperation(value = "创建药房管理-药品入库批次")
    @LogRecordAnnotation(bizModule = "ihc:medicineIn:create", operateType = LogOperateType.CREATE, title = "创建药房管理-药品入库批次",
            success = "创建药房管理-药品入库批次成功", fail = "创建药房管理-药品入库批次失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createMedicineIn(@Valid @RequestBody List<MedicineInSaveReqVO> medicineInSaveReqVOList) {
        medicineInSaveReqVOList.forEach(medicineInSaveReqVO -> {
            medicineInService.createMedicineIn(medicineInSaveReqVO);
        });
        return success("提交成功");
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新药房管理-药品入库批次")
    @LogRecordAnnotation(bizModule = "ihc:medicineIn:update", operateType = LogOperateType.UPDATE, title = "更新药房管理-药品入库批次",
            success = "更新药房管理-药品入库批次成功", fail = "更新药房管理-药品入库批次失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateMedicineIn(@Valid @RequestBody MedicineInSaveReqVO updateReqVO) {
        medicineInService.updateMedicineIn(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除药房管理-药品入库批次")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:medicineIn:delete", operateType = LogOperateType.DELETE, title = "删除药房管理-药品入库批次",
            success = "删除药房管理-药品入库批次成功", fail = "删除药房管理-药品入库批次失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteMedicineIn(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            medicineInService.deleteMedicineIn(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得药房管理-药品入库批次")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:medicineIn:get", operateType = LogOperateType.QUERY, title = "获取药房管理-药品入库批次", bizNo = "{{#id}}", success = "获取药房管理-药品入库批次成功", fail = "获取药房管理-药品入库批次失败", extraInfo = "{{#id}}")
    public CommonResult<MedicineInRespVO> getMedicineIn(@RequestParam("id") String id) {
        MedicineInDO medicineIn = medicineInService.getMedicineIn(id);
        return success(BeanUtils.toBean(medicineIn, MedicineInRespVO.class));
    }

    @GetMapping("/getDetail")
    @ApiOperation(value = "获得药房管理-药品入库批次详情")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:medicineIn:get", operateType = LogOperateType.QUERY, title = "获取药房管理-药品入库批次详情", bizNo = "{{#id}}", success = "获取药房管理-药品入库批次详情成功", fail = "获取药房管理-药品入库批次详情失败", extraInfo = "{{#id}}")
    public CommonResult<MedicineInRespVO> getMedicineInDetail(@RequestParam("id") String id) {
        MedicineInDO medicineIn = medicineInService.getMedicineIn(id);
        if (ObjectUtil.isEmpty(medicineIn)) {
            return success(new MedicineInRespVO());
        }
        MedicineInRespVO medicineInVO = BeanUtils.toBean(medicineIn, MedicineInRespVO.class);
        medicineInVO.setMedicine(medicineService.getMedicineDetail(medicineInVO.getMedicineId()));
        return success(medicineInVO);
    }

    @GetMapping("/page")
    @ApiOperation(value = "获得药房管理-药品入库批次分页")
    @LogRecordAnnotation(bizModule = "ihc:medicineIn:page", operateType = LogOperateType.QUERY, title = "获得药房管理-药品入库批次分页",
            success = "获得药房管理-药品入库批次分页成功", fail = "获得药房管理-药品入库批次分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<MedicineInRespVO>> getMedicineInPage(@Valid MedicineInPageReqVO pageReqVO) {
        PageResult<MedicineInDO> pageResult = medicineInService.getMedicineInPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MedicineInRespVO.class));
    }

    @GetMapping("/list")
    @ApiOperation(value = "获得药房管理-药品入库批次列表")
    @LogRecordAnnotation(bizModule = "ihc:medicineIn:list", operateType = LogOperateType.QUERY, title = "获得药房管理-药品入库批次列表",
            success = "获得药房管理-药品入库批次列表成功", fail = "获得药房管理-药品入库批次列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<MedicineInRespVO>> getMedicineInList(@Valid MedicineInListReqVO listReqVO) {
        List<MedicineInDO> list = medicineInService.getMedicineInList(listReqVO);
        return success(BeanUtils.toBean(list, MedicineInRespVO.class));
    }

    @ApiOperation(value = "Excel文件数据导入")
    @PostMapping(value = "/importExcel")
    public CommonResult importExcel(MultipartFile file) throws Exception {

        List<MedicineInImportVO> list = new ArrayList<>();
        List<MedicineInImportVO> result = new ArrayList<>();
        MedicineInImportListVO medicineInImportListVO = new MedicineInImportListVO();
        EasyExcel.read(file.getInputStream(),
                        MedicineInImportVO.class,
                        new MedicineInImportListener(list, medicineInImportListVO))
                .sheet()
                .headRowNumber(4)
                .doRead();
        medicineInImportListVO.setMedicineInImportVOList(list);
        medicineInImportListVO.setMedicineInImportVOErrorList(result);
        Handler<MedicineInImportListVO> chain = chainBuilder.buildChain(Arrays.asList(validateHandler, saveHandler));
        MedicineInImportListVO handleResult = chain.handle(medicineInImportListVO);
        List<MedicineInImportVO> medicineInImportVOErrorList = handleResult.getMedicineInImportVOErrorList();
        if (!medicineInImportVOErrorList.isEmpty()) {
            return CommonResult.error(new Integer(501), handleResult);
        }
        return CommonResult.success("导入药品数据成功");
    }

    @ApiOperation(value = "Excel文件数据重新导入")
    @PostMapping(value = "/reImportExcel")
    public CommonResult reImportExcel(@RequestBody MedicineInImportListVO medicineInImportListVO) throws Exception {
        medicineInImportListVO.setMedicineInImportVOList(medicineInImportListVO.getMedicineInImportVOErrorList());
        medicineInImportListVO.setMedicineInImportVOErrorList(new ArrayList<>());
        Handler<MedicineInImportListVO> chain = chainBuilder.buildChain(Arrays.asList(validateHandler, saveHandler));
        MedicineInImportListVO handleResult = chain.handle(medicineInImportListVO);
        List<MedicineInImportVO> medicineInImportVOErrorList = handleResult.getMedicineInImportVOErrorList();
        if (!medicineInImportVOErrorList.isEmpty()) {
            return CommonResult.error(501, medicineInImportVOErrorList);
        }
        return CommonResult.success("导入药品数据成功");
    }

    @PostMapping("/ypbsList")
    @ApiOperation(value = "获得药房管理-药品报损-药品批次")
    public CommonResult<List<MedicineInRespVO>> ypbsList(@Valid @RequestBody MedicineInYpbsReqVO listReqVO) {
        List<MedicineInDO> list = medicineInService.ypbsList(listReqVO);
        return success(BeanUtils.toBean(list, MedicineInRespVO.class));
    }

}
