package com.rs.module.ihc.controller.admin.pm;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.ihc.service.ipm.IhcsMedicalDeliveryRemindService;
import com.rs.module.ihc.service.ipm.dto.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Validated
@RestController
@AllArgsConstructor
@RequestMapping("/ihc/delivery/remind")
@Api(tags = "送药管理-送药提醒")
public class IhcsMedicalDeliveryRemindController {

    private final IhcsMedicalDeliveryRemindService ihcsMedicalDeliveryRemindService;

    @PostMapping("/execute")
    @ApiOperation("执行医嘱处方/监室送药登记")
    public CommonResult<Void> executePrescribe(@RequestBody @Valid ExecutePrescribeDTO executePrescribeDTO) {
        ihcsMedicalDeliveryRemindService.executePrescribe(executePrescribeDTO);
        return CommonResult.success();
    }


    /**
     * 监室送药登记
     * @param batchExecutePrescribeDTO
     * @return
     */
    @PostMapping("/batchExecute")
    @ApiOperation("批量执行医嘱处方/监室送药登记")
    public CommonResult<Void> batchExecutePrescribe(@RequestBody @Valid BatchExecutePrescribeDTO batchExecutePrescribeDTO) {
        ihcsMedicalDeliveryRemindService.batchExecutePrescribe(batchExecutePrescribeDTO);
        return CommonResult.success();
    }


    /**
     * 临床送药登记
     * @param prescribeClinicalRegisterDTO
     * @return
     */
    @PostMapping("/clinical/register")
    @ApiOperation("临床送药登记")
    public CommonResult<Void> prescribeClinicalRegister(@RequestBody @Valid PrescribeClinicalRegisterDTO prescribeClinicalRegisterDTO) {
        ihcsMedicalDeliveryRemindService.prescribeClinicalRegister(prescribeClinicalRegisterDTO);
        return CommonResult.success();
    }


    /**
     * 服药签名
     * @param executePrescribeSignDTO
     * @return
     */
    @PostMapping("/sign")
    @ApiOperation("服药签名")
    public CommonResult<Void> executePrescribeSign(@RequestBody @Valid ExecutePrescribeSignDTO executePrescribeSignDTO) {
        ihcsMedicalDeliveryRemindService.executePrescribeSign(executePrescribeSignDTO);
        return CommonResult.success();
    }

    /**
     * 拒绝领药
     * @param refuseExecutePrescribeDTO
     * @return
     */
    @PostMapping("/execute/refuse")
    @ApiOperation("拒绝领药/服药")
    public CommonResult<Void> refuseExecutePrescribe(@RequestBody @Valid RefuseExecutePrescribeDTO refuseExecutePrescribeDTO) {
        ihcsMedicalDeliveryRemindService.refuseExecutePrescribe(refuseExecutePrescribeDTO);
        return CommonResult.success();
    }


    /**
     * 拒绝服药签名
     * @param signRefuseExecutePrescribeDTO
     * @return
     */
    @PostMapping("/refuse/sign")
    @ApiOperation("拒绝领药/服药签名")
    public CommonResult<Void> signRefuseExecutePrescribe(@RequestBody @Valid SignRefuseExecutePrescribeDTO signRefuseExecutePrescribeDTO) {
        ihcsMedicalDeliveryRemindService.signRefuseExecutePrescribe(signRefuseExecutePrescribeDTO);
        return CommonResult.success();
    }

    /**
     * 保存服药视频
     * @param executePrescribeVideoDTO
     * @return
     */

    @PostMapping("/saveVideo")
    @ApiOperation("保存服药视频")
    public CommonResult<Void> executePrescribeVideo(@RequestBody @Valid ExecutePrescribeVideoDTO executePrescribeVideoDTO) {
        ihcsMedicalDeliveryRemindService.executePrescribeVideo(executePrescribeVideoDTO);
        return CommonResult.success();
    }

}
