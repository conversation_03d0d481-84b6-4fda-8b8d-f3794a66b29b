package com.rs.module.ihc;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import com.mzt.bsp.logapi.starter.annotation.EnableLogRecord;
import com.rs.framework.common.util.spring.SpringUtils;
import org.dromara.x.file.storage.spring.EnableFileStorage;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.net.UnknownHostException;

/**
 * 医疗管理系统启动类
 * <AUTHOR>
 *
 */
@SpringBootApplication(exclude = {DruidDataSourceAutoConfigure.class, MongoAutoConfiguration.class, MongoDataAutoConfiguration.class})
@ComponentScan(value = {"com.rs.*", "com.bsp.*"})
@EnableLogRecord(systemMark = "ihc")
@EnableFileStorage
@EnableScheduling
public class IhcServerApplication {

	public static void main(String[] args) throws UnknownHostException {
		ConfigurableApplicationContext application = SpringApplication.run(IhcServerApplication.class, args);
		SpringUtils.printStartLog(application);
	}
}
