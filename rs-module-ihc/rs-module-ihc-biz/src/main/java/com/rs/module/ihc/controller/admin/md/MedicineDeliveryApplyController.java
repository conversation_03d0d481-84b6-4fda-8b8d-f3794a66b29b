package com.rs.module.ihc.controller.admin.md;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.vo.SimpleApproveReqVO;
import com.rs.module.ihc.controller.admin.md.vo.*;
import com.rs.module.ihc.entity.md.MedicineDeliveryApplyDO;
import com.rs.module.ihc.service.md.MedicineDeliveryApplyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "药品顾送管理-药品顾送申请")
@RestController
@RequestMapping("/ihc/md/medicineDeliveryApply")
@Validated
public class MedicineDeliveryApplyController {

    @Resource
    private MedicineDeliveryApplyService medicineDeliveryApplyService;

    @PostMapping("/create")
    @ApiOperation(value = "创建药品顾送管理-药品顾送申请")
    @LogRecordAnnotation(bizModule = "ihc:mdmedicineDeliveryApply:create", operateType = LogOperateType.CREATE, title = "创建药房管理-药品信息",
            success = "创建药房管理-药品信息成功", fail = "创建药房管理-药品信息失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createMedicineDeliveryApply(@Valid @RequestBody MedicineDeliveryApplySaveReqVO createReqVO) {
        return success(medicineDeliveryApplyService.createMedicineDeliveryApply(createReqVO));
    }


    @PostMapping("/approve")
    @ApiOperation(value = "药品顾送-审批")
    @LogRecordAnnotation(bizModule = "acp:mdmedicineDeliveryApply:approve", operateType = LogOperateType.UPDATE, title = "医疗-药品顾送-审批",
            success = "医疗-药品顾送-审批成功", fail = "管教业务-处罚呈批-领导审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#approveReqVO}}")
    public CommonResult<Boolean> approve(@Valid @RequestBody SimpleApproveReqVO approveReqVO) {
        medicineDeliveryApplyService.approve(approveReqVO);
        return success(true);
    }


    @PostMapping("/regInfo")
    @ApiOperation(value = "药品顾送登记")
    @LogRecordAnnotation(bizModule = "acp:mdmedicineDeliveryApply:regInfo", operateType = LogOperateType.UPDATE, title = "医疗-药品顾送-顾送登记",
            success = "医疗-药品顾送-顾送登记", fail = "医疗-药品顾送-顾送登记-失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#regInfoReqVO}}")
    public CommonResult<Boolean> regInfo(@Valid @RequestBody MedicineDeliveryApplyRegInfoReqVO regInfoReqVO) {
        medicineDeliveryApplyService.regInfo(regInfoReqVO);
        return success(true);
    }

    @PostMapping("/abnormalRegInfo")
    @ApiOperation(value = "药品顾送-终止异常登记")
    @LogRecordAnnotation(bizModule = "acp:mdmedicineDeliveryApply:abnormalRegInfo", operateType = LogOperateType.UPDATE, title = "医疗-药品顾送-顾送登记",
            success = "医疗-药品顾送-顾送登记", fail = "医疗-药品顾送-顾送登记-失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#regInfoReqVO}}")
    public CommonResult<Boolean> abnormalRegInfo(@Valid @RequestBody MedicineDeliveryApplyAbnormalReqVO regInfoReqVO) {
        medicineDeliveryApplyService.abnormalRegInfo(regInfoReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获取详情-药品申请")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:mdmedicineDeliveryApply:get", operateType = LogOperateType.QUERY, title = "医疗-药品顾送-获取详情",
            bizNo = "{{#id}}", success = "医疗-药品顾送申请获取成功", fail = "医疗-药品顾送申请获取-获取失败", extraInfo = "{{#id}}")
    public CommonResult<MedicineDeliveryApplyRespVO> getMedicineDeliveryApply(@RequestParam("id") String id) {
        MedicineDeliveryApplyRespVO medicineDeliveryApply = medicineDeliveryApplyService.getMedicineDeliveryApply(id);
        return success(medicineDeliveryApply);
    }


    @PostMapping("/page")
    @ApiOperation(value = "获得药品顾送管理-药品顾送申请分页")
    public CommonResult<PageResult<MedicineDeliveryApplyRespVO>> getMedicineDeliveryApplyPage(@Valid @RequestBody MedicineDeliveryApplyPageReqVO pageReqVO) {
        PageResult<MedicineDeliveryApplyDO> pageResult = medicineDeliveryApplyService.getMedicineDeliveryApplyDefaultPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MedicineDeliveryApplyRespVO.class));
    }

}
