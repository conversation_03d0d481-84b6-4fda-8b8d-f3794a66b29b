package com.rs.module.ihc.controller.admin.idm;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.idm.vo.RegularInspectionPageReqVO;
import com.rs.module.ihc.controller.admin.idm.vo.RegularInspectionRespVO;
import com.rs.module.ihc.controller.admin.idm.vo.RegularInspectionSaveReqVO;
import com.rs.module.ihc.entity.idm.RegularInspectionDO;
import com.rs.module.ihc.service.idm.RegularInspectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "传染病管理-定期检查登记")
@RestController
@RequestMapping("/ihc/idm/regularInspection")
@Validated
public class RegularInspectionController {

    @Resource
    private RegularInspectionService regularInspectionService;

    @PostMapping("/create")
    @ApiOperation(value = "传染病管理-艾滋病-新增定期检测登记")
    @LogRecordAnnotation(bizModule = "ihc:idm:regularInspection-create", operateType = LogOperateType.CREATE, title = "传染病管理-定期检查登记",
            success = "传染病管理-定期检查登记-成功", fail = "传染病管理-定期检查登记-错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createRegularInspection(@Valid @RequestBody RegularInspectionSaveReqVO createReqVO) {
        return success(regularInspectionService.createRegularInspection(createReqVO));
    }


    @GetMapping("/get")
    @ApiOperation(value = "获得传染病管理-定期检查登记")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:idm:regularInspection-get", operateType = LogOperateType.QUERY, title = "传染病管理-定期检查登记",
            success = "传染病管理-定期检查登记-成功", fail = "传染病管理-定期检查登记-错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    public CommonResult<RegularInspectionRespVO> getRegularInspection(@RequestParam("id") String id) {
        RegularInspectionDO regularInspection = regularInspectionService.getRegularInspection(id);
        return success(BeanUtils.toBean(regularInspection, RegularInspectionRespVO.class));
    }


    @GetMapping("/page")
    @ApiOperation(value = "获得传染病管理-定期检查登记-列表查询")
    @LogRecordAnnotation(bizModule = "ihc:idm:regularInspection:page", operateType = LogOperateType.QUERY, title = "获得所内就医-现场巡诊计划分页",
            success = "获得传染病管理-定期检查登记成功", fail = "获得传染病管理-定期检查登记失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<RegularInspectionRespVO>> getVisitPlanPage(@Valid RegularInspectionPageReqVO pageReqVO) {
        PageResult<RegularInspectionDO> pageResult = regularInspectionService.getRegularInspectionPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, RegularInspectionRespVO.class));
    }

}
