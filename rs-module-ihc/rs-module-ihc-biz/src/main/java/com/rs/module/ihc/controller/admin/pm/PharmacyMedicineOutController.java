package com.rs.module.ihc.controller.admin.pm;

import cn.hutool.core.util.ObjectUtil;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicineOutListReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicineOutPageReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicineOutRespVO;
import com.rs.module.ihc.controller.admin.pm.vo.PharmacyMedicineOutSaveReqVO;
import com.rs.module.ihc.entity.pm.PharmacyMedicineOutDO;
import com.rs.module.ihc.service.pm.PharmacyMedicineInService;
import com.rs.module.ihc.service.pm.PharmacyMedicineOutService;
import com.rs.module.ihc.service.pm.PharmacyMedicineService;
import com.rs.module.ihc.service.pm.dto.BatchSaveMedicineOutDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "大药房管理-药品出库")
@RestController
@RequestMapping("/ihc/pm/pharmacyMedicineOut")
@Validated
public class PharmacyMedicineOutController {

    @Resource
    private PharmacyMedicineOutService pharmacyMedicineOutService;

    @Resource
    private PharmacyMedicineInService  medicineInService;

    @Resource
    private PharmacyMedicineService medicineService;

    @PostMapping("/create")
    @ApiOperation(value = "创建大药房管理-药品出库")
    @LogRecordAnnotation(bizModule = "ihc:pharmacyMedicineOut:create", operateType = LogOperateType.CREATE, title = "创建大药房管理-药品出库",
            success = "创建大药房管理-药品出库成功", fail = "创建大药房管理-药品出库失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createPharmacyMedicineOut(@Valid @RequestBody PharmacyMedicineOutSaveReqVO createReqVO) {
        return success(pharmacyMedicineOutService.createPharmacyMedicineOut(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新大药房管理-药品出库")
    @LogRecordAnnotation(bizModule = "ihc:pharmacyMedicineOut:update", operateType = LogOperateType.UPDATE, title = "更新大药房管理-药品出库",
            success = "更新大药房管理-药品出库成功", fail = "更新大药房管理-药品出库失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updatePharmacyMedicineOut(@Valid @RequestBody PharmacyMedicineOutSaveReqVO updateReqVO) {
        pharmacyMedicineOutService.updatePharmacyMedicineOut(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除大药房管理-药品出库")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:pharmacyMedicineOut:delete", operateType = LogOperateType.DELETE, title = "更新大药房管理-药品出库",
            success = "更新大药房管理-药品出库成功", fail = "更新大药房管理-药品出库失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deletePharmacyMedicineOut(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           pharmacyMedicineOutService.deletePharmacyMedicineOut(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得大药房管理-药品出库")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:pharmacyMedicineOut:get",
            operateType = LogOperateType.QUERY, title = "获得大药房管理-药品出库", bizNo = "{{#id}}",
            success = "获得大药房管理-药品出库成功", fail = "获得大药房管理-药品出库失败", extraInfo = "{{#id}}")
    public CommonResult<PharmacyMedicineOutRespVO> getPharmacyMedicineOut(@RequestParam("id") String id) {
        PharmacyMedicineOutDO pharmacyMedicineOut = pharmacyMedicineOutService.getPharmacyMedicineOut(id);
        return success(BeanUtils.toBean(pharmacyMedicineOut, PharmacyMedicineOutRespVO.class));
    }

    @GetMapping("/getDetail")
    @ApiOperation(value = "获得药房管理-药品出库详情")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:pharmacyMedicineOut:get", operateType = LogOperateType.QUERY,
            title = "获得大药房管理-药品出库详情", bizNo = "{{#id}}", success = "获得大药房管理-药品出库详情成功", fail = "获取药房管理-药品出库详情失败", extraInfo = "{{#id}}")
    public CommonResult<PharmacyMedicineOutRespVO> getMedicineOutDetail(@RequestParam("id") String id) {
        PharmacyMedicineOutDO medicineOut = pharmacyMedicineOutService.getPharmacyMedicineOut(id);
        if (ObjectUtil.isEmpty(medicineOut)) {
            return success(null);
        }
        PharmacyMedicineOutRespVO medicineOutVO = BeanUtils.toBean(medicineOut, PharmacyMedicineOutRespVO.class);
        medicineOutVO.setMedicineIn(medicineInService.getMedicineInDetail(medicineOut.getMedicineInId()));
        medicineOutVO.setMedicine(medicineService.getMedicineDetail(medicineOut.getMedicineId()));
        return success(medicineOutVO);
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得大药房管理-药品出库分页")
    @LogRecordAnnotation(bizModule = "ihc:pharmacyMedicineOut:page", operateType = LogOperateType.QUERY, title = "获得大药房管理-药品出库分页",
            success = "获得大药房管理-药品出库分页成功", fail = "获得大药房管理-药品出库分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<PharmacyMedicineOutRespVO>> getPharmacyMedicineOutPage(@Valid @RequestBody PharmacyMedicineOutPageReqVO pageReqVO) {
        PageResult<PharmacyMedicineOutDO> pageResult = pharmacyMedicineOutService.getPharmacyMedicineOutPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PharmacyMedicineOutRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得大药房管理-药品出库列表")
    @LogRecordAnnotation(bizModule = "ihc:pharmacyMedicineOut:list", operateType = LogOperateType.QUERY, title = "获得大药房管理-药品出库列表",
            success = "获得大药房管理-药品出库列表成功", fail = "获得大药房管理-药品出库列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<PharmacyMedicineOutRespVO>> getPharmacyMedicineOutList(@Valid @RequestBody PharmacyMedicineOutListReqVO listReqVO) {
        List<PharmacyMedicineOutDO> list = pharmacyMedicineOutService.getPharmacyMedicineOutList(listReqVO);
        return success(BeanUtils.toBean(list, PharmacyMedicineOutRespVO.class));
    }

    @PostMapping("/batchSave")
    @ApiOperation("批量新增出库批次")
    public CommonResult<Boolean> batchSaveMedicineOut(@RequestBody @Valid BatchSaveMedicineOutDTO batchSaveMedicineOutDTO) {
        pharmacyMedicineOutService.batchSaveMedicineOut(batchSaveMedicineOutDTO.getMedicineOutList(),
                batchSaveMedicineOutDTO.getId());
        return success(true);
    }
}
