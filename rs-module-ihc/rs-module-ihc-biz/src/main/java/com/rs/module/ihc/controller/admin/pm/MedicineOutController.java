package com.rs.module.ihc.controller.admin.pm;

import cn.hutool.core.util.ObjectUtil;
import com.rs.module.ihc.service.pm.MedicineInService;
import com.rs.module.ihc.service.pm.MedicineService;
import com.rs.module.ihc.service.pm.dto.BatchSaveMedicineOutDTO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.entity.pm.MedicineOutDO;
import com.rs.module.ihc.service.pm.MedicineOutService;

@Api(tags = "管理后台 - 药房管理-药品出库")
@RestController
@RequestMapping("/ihc/pm/medicineOut")
@Validated
public class MedicineOutController {

    @Resource
    private MedicineOutService medicineOutService;
    @Resource
    private MedicineInService medicineInService;
    @Resource
    private MedicineService medicineService;

    @PostMapping("/create")
    @ApiOperation(value = "创建药房管理-药品出库")
    @LogRecordAnnotation(bizModule = "ihc:medicineOut:create", operateType = LogOperateType.CREATE, title = "创建药房管理-药品出库",
    success = "创建药房管理-药品出库成功", fail = "创建药房管理-药品出库失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createMedicineOut(@Valid @RequestBody MedicineOutSaveReqVO createReqVO) {
        return success(medicineOutService.createMedicineOut(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新药房管理-药品出库")
    @LogRecordAnnotation(bizModule = "ihc:medicineOut:update", operateType = LogOperateType.UPDATE, title = "更新药房管理-药品出库",
    success = "更新药房管理-药品出库成功", fail = "更新药房管理-药品出库失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateMedicineOut(@Valid @RequestBody MedicineOutSaveReqVO updateReqVO) {
        medicineOutService.updateMedicineOut(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除药房管理-药品出库")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:medicineOut:delete", operateType = LogOperateType.DELETE, title = "删除药房管理-药品出库",
    success = "删除药房管理-药品出库成功", fail = "删除药房管理-药品出库失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteMedicineOut(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           medicineOutService.deleteMedicineOut(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得药房管理-药品出库")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:medicineOut:get", operateType = LogOperateType.QUERY, title = "获取药房管理-药品出库", bizNo = "{{#id}}", success = "获取药房管理-药品出库成功", fail = "获取药房管理-药品出库失败", extraInfo = "{{#id}}")
    public CommonResult<MedicineOutRespVO> getMedicineOut(@RequestParam("id") String id) {
        MedicineOutDO medicineOut = medicineOutService.getMedicineOut(id);
        return success(BeanUtils.toBean(medicineOut, MedicineOutRespVO.class));
    }

    @GetMapping("/getDetail")
    @ApiOperation(value = "获得药房管理-药品出库详情")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:medicineOut:get", operateType = LogOperateType.QUERY, title = "获取药房管理-药品出库详情", bizNo = "{{#id}}", success = "获取药房管理-药品出库详情成功", fail = "获取药房管理-药品出库详情失败", extraInfo = "{{#id}}")
    public CommonResult<MedicineOutRespVO> getMedicineOutDetail(@RequestParam("id") String id) {
        MedicineOutDO medicineOut = medicineOutService.getMedicineOut(id);
        if (ObjectUtil.isEmpty(medicineOut)) {
            return success(new MedicineOutRespVO());
        }
        MedicineOutRespVO medicineOutVO = BeanUtils.toBean(medicineOut, MedicineOutRespVO.class);
        medicineOutVO.setMedicineIn(medicineInService.getMedicineInDetail(medicineOut.getMedicineInId()));
        medicineOutVO.setMedicine(medicineService.getMedicineDetail(medicineOut.getMedicineId()));
        return success(medicineOutVO);
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得药房管理-药品出库分页")
    @LogRecordAnnotation(bizModule = "ihc:medicineOut:page", operateType = LogOperateType.QUERY, title = "获得药房管理-药品出库分页",
    success = "获得药房管理-药品出库分页成功", fail = "获得药房管理-药品出库分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<MedicineOutRespVO>> getMedicineOutPage(@Valid @RequestBody MedicineOutPageReqVO pageReqVO) {
        PageResult<MedicineOutDO> pageResult = medicineOutService.getMedicineOutPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MedicineOutRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得药房管理-药品出库列表")
    @LogRecordAnnotation(bizModule = "ihc:medicineOut:list", operateType = LogOperateType.QUERY, title = "获得药房管理-药品出库列表",
    success = "获得药房管理-药品出库列表成功", fail = "获得药房管理-药品出库列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<MedicineOutRespVO>> getMedicineOutList(@Valid @RequestBody MedicineOutListReqVO listReqVO) {
    List<MedicineOutDO> list = medicineOutService.getMedicineOutList(listReqVO);
        return success(BeanUtils.toBean(list, MedicineOutRespVO.class));
    }
    //TODO 药品出库
    @PostMapping("/batchSave")
    @ApiOperation("批量新增出库批次")
    public CommonResult<Void> batchSaveMedicineOut(@RequestBody @Valid BatchSaveMedicineOutDTO batchSaveMedicineOutDTO) {
        medicineOutService.batchSaveMedicineOut(batchSaveMedicineOutDTO.getMedicineOutList(),
                batchSaveMedicineOutDTO.getId());
        return CommonResult.success();
    }

}
