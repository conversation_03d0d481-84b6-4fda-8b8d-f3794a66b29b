package com.rs.module.ihc.controller.admin.test;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bsp.common.cache.RedisClient;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.ihc.entity.test.IhcPmMedicineTest;
import com.rs.module.ihc.service.test.IhcPmMedicineTestService;

/**
 * 药品控制器(测试)
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/rs/ihc/pmMedicine")
public class IhcPmMedicineTestController {
	
	@Resource
	IhcPmMedicineTestService service;
	
	/**
	 * 获取表数据
	 * @return
	 */
	@GetMapping("/getTableData")
	public CommonResult<List<IhcPmMedicineTest>> getTableData(){
		List<IhcPmMedicineTest> list = service.list();
		return CommonResult.success(list);
	}
	
	/**
	 * 获取缓存数据
	 * @return
	 */
	@GetMapping("/getRedisData")
	public CommonResult<String> getRedisData(){
		String initInfo = RedisClient.get("initSystem:bsp");
		return CommonResult.success(initInfo);
	}
}
