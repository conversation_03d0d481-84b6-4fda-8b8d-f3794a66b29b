package com.rs.module.ihc.controller.admin.pm;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.handler.ChainBuilder;
import com.rs.framework.common.handler.Handler;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.entity.pm.PharmacyMedicineInDO;
import com.rs.module.ihc.handler.pm.medicine.in.PharmacyValidateHandler;
import com.rs.module.ihc.handler.pm.medicine.in.SaveHandler;
import com.rs.module.ihc.listener.pm.MedicineInImportListener;
import com.rs.module.ihc.service.pm.PharmacyMedicineInService;
import com.rs.module.ihc.service.pm.PharmacyMedicineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "大药房管理-药品入库批次")
@RestController
@RequestMapping("/ihc/pm/pharmacyMedicineIn")
@Validated
public class PharmacyMedicineInController {

    @Resource
    private PharmacyMedicineInService pharmacyMedicineInService;

    @Resource
    private PharmacyMedicineService pharmacyMedicineService;

    @Resource
    private ChainBuilder<MedicineInImportListVO> chainBuilder;

    @Autowired
    private SaveHandler saveHandler;

    @Autowired
    private PharmacyValidateHandler validateHandler;

    @PostMapping("/create")
    @ApiOperation(value = "创建大药房管理-药品入库批次")
    @LogRecordAnnotation(bizModule = "ihc:pharmacyMedicineIn:create", operateType = LogOperateType.CREATE, title = "创建大药房管理-药品入库批次",
            success = "创建大药房管理-药品入库批次成功", fail = "创建大药房管理-药品入库批次失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#medicineInSaveReqVOList}}")
    public CommonResult<String> createPharmacyMedicineIn(@Valid @RequestBody List<PharmacyMedicineInSaveReqVO> medicineInSaveReqVOList) {
        medicineInSaveReqVOList.forEach(medicineInSaveReqVO -> {
            pharmacyMedicineInService.createPharmacyMedicineIn(medicineInSaveReqVO);
        });
        return success("提交成功");
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新大药房管理-药品入库批次")
    @LogRecordAnnotation(bizModule = "ihc:pharmacyMedicineIn:update", operateType = LogOperateType.UPDATE, title = "更新大药房管理-药品入库批次",
            success = "更新大药房管理-药品入库批次成功", fail = "更新大药房管理-药品入库批次失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updatePharmacyMedicineIn(@Valid @RequestBody PharmacyMedicineInSaveReqVO updateReqVO) {
        pharmacyMedicineInService.updatePharmacyMedicineIn(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除大药房管理-药品入库批次")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:pharmacyMedicineIn:delete", operateType = LogOperateType.DELETE, title = "删除大药房管理-药品入库批次",
            success = "删除大药房管理-药品入库批次成功", fail = "删除大药房管理-药品入库批次失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deletePharmacyMedicineIn(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           pharmacyMedicineInService.deletePharmacyMedicineIn(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得大药房管理-药品入库批次")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:pharmacyMedicineIn:get", operateType = LogOperateType.QUERY,
            title = "获得大药房管理-药品入库批次", bizNo = "{{#id}}", success = "获得大药房管理-药品入库批次成功", fail = "获得大药房管理-药品入库批次失败", extraInfo = "{{#id}}")
    public CommonResult<PharmacyMedicineInRespVO> getPharmacyMedicineIn(@RequestParam("id") String id) {
        PharmacyMedicineInDO pharmacyMedicineIn = pharmacyMedicineInService.getPharmacyMedicineIn(id);
        return success(BeanUtils.toBean(pharmacyMedicineIn, PharmacyMedicineInRespVO.class));
    }

    @GetMapping("/getDetail")
    @ApiOperation(value = "获得大药房管理-药品入库批次详情")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:pharmacyMedicineIn:getDetail", operateType = LogOperateType.QUERY,
            title = "获得大药房管理-药品入库批次详情", bizNo = "{{#id}}", success = "获得大药房管理-药品入库批次详情成功", fail = "获得大药房管理-药品入库批次详情失败", extraInfo = "{{#id}}")
    public CommonResult<PharmacyMedicineInRespVO> getMedicineInDetail(@RequestParam("id") String id) {
        PharmacyMedicineInDO medicineIn = pharmacyMedicineInService.getPharmacyMedicineIn(id);
        if (ObjectUtil.isEmpty(medicineIn)) {
            return success(null);
        }
        PharmacyMedicineInRespVO medicineInVO = BeanUtils.toBean(medicineIn, PharmacyMedicineInRespVO.class);
        medicineInVO.setMedicine(pharmacyMedicineService.getMedicineDetail(medicineInVO.getMedicineId()));
        return success(medicineInVO);
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得大药房管理-药品入库批次分页")
    @LogRecordAnnotation(bizModule = "ihc:pharmacyMedicineIn:page", operateType = LogOperateType.QUERY, title = "获得药房管理-药品入库批次分页",
            success = "获得药房管理-药品入库批次分页成功", fail = "获得药房管理-药品入库批次分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<PharmacyMedicineInRespVO>> getPharmacyMedicineInPage(@Valid @RequestBody PharmacyMedicineInPageReqVO pageReqVO) {
        PageResult<PharmacyMedicineInDO> pageResult = pharmacyMedicineInService.getPharmacyMedicineInPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PharmacyMedicineInRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得大药房管理-药品入库批次列表")
    @LogRecordAnnotation(bizModule = "ihc:pharmacyMedicineIn:list", operateType = LogOperateType.QUERY, title = "获得大药房管理-药品入库批次列表",
            success = "获得大药房管理-药品入库批次列表成功", fail = "获得大药房管理-药品入库批次列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<PharmacyMedicineInRespVO>> getPharmacyMedicineInList(@Valid @RequestBody PharmacyMedicineInListReqVO listReqVO) {
        List<PharmacyMedicineInDO> list = pharmacyMedicineInService.getPharmacyMedicineInList(listReqVO);
        return success(BeanUtils.toBean(list, PharmacyMedicineInRespVO.class));
    }


    @ApiOperation(value = "Excel文件数据导入")
    @PostMapping(value = "/importExcel")
    public CommonResult importExcel(MultipartFile file) throws Exception {
        List<MedicineInImportVO> list = new ArrayList<>();
        List<MedicineInImportVO> result = new ArrayList<>();
        MedicineInImportListVO medicineInImportListVO = new MedicineInImportListVO();
        EasyExcel.read(file.getInputStream(),
                        MedicineInImportVO.class,
                        new MedicineInImportListener(list, medicineInImportListVO))
                .sheet()
                .headRowNumber(4)
                .doRead();
        medicineInImportListVO.setMedicineInImportVOList(list);
        medicineInImportListVO.setMedicineInImportVOErrorList(result);
        Handler<MedicineInImportListVO> chain = chainBuilder.buildChain(Arrays.asList(validateHandler, saveHandler));
        MedicineInImportListVO handleResult = chain.handle(medicineInImportListVO);
        List<MedicineInImportVO> medicineInImportVOErrorList = handleResult.getMedicineInImportVOErrorList();
        if (!medicineInImportVOErrorList.isEmpty()) {
            return CommonResult.error(new Integer(501), handleResult);
        }
        return CommonResult.success("导入药品数据成功");
    }

    @ApiOperation(value = "Excel文件数据重新导入")
    @PostMapping(value = "/reImportExcel")
    public CommonResult reImportExcel(@RequestBody MedicineInImportListVO medicineInImportListVO) throws Exception {
        medicineInImportListVO.setMedicineInImportVOList(medicineInImportListVO.getMedicineInImportVOErrorList());
        medicineInImportListVO.setMedicineInImportVOErrorList(new ArrayList<>());
        Handler<MedicineInImportListVO> chain = chainBuilder.buildChain(Arrays.asList(validateHandler, saveHandler));
        MedicineInImportListVO handleResult = chain.handle(medicineInImportListVO);
        List<MedicineInImportVO> medicineInImportVOErrorList = handleResult.getMedicineInImportVOErrorList();
        if (!medicineInImportVOErrorList.isEmpty()) {
            return CommonResult.error(501, medicineInImportVOErrorList);
        }
        return CommonResult.success("导入药品数据成功");
    }

    @PostMapping("/ypbsList")
    @ApiOperation(value = "获得药房管理-药品报损-药品批次")
    public CommonResult<List<PharmacyMedicineInRespVO>> ypbsList(@Valid @RequestBody MedicineInYpbsReqVO listReqVO) {
        List<PharmacyMedicineInDO> list = pharmacyMedicineInService.ypbsList(listReqVO);
        return success(BeanUtils.toBean(list, PharmacyMedicineInRespVO.class));
    }








}
