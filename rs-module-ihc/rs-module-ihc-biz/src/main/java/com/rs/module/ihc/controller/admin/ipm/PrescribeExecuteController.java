package com.rs.module.ihc.controller.admin.ipm;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.entity.ipm.PrescribeExecuteDO;
import com.rs.module.ihc.service.ipm.PrescribeExecuteService;

@Api(tags = "管理后台 - 所内就医-医嘱/处方执行记录")
@RestController
@RequestMapping("/ihc/ipm/prescribeExecute")
@Validated
public class PrescribeExecuteController {

    @Resource
    private PrescribeExecuteService prescribeExecuteService;

    @PostMapping("/create")
    @ApiOperation(value = "创建所内就医-医嘱/处方执行记录")
    @LogRecordAnnotation(bizModule = "ihc:prescribeExecute:create", operateType = LogOperateType.CREATE, title = "创建所内就医-医嘱/处方执行记录",
    success = "创建所内就医-医嘱/处方执行记录成功", fail = "创建所内就医-医嘱/处方执行记录失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createPrescribeExecute(@Valid @RequestBody PrescribeExecuteSaveReqVO createReqVO) {
        return success(prescribeExecuteService.createPrescribeExecute(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新所内就医-医嘱/处方执行记录")
    @LogRecordAnnotation(bizModule = "ihc:prescribeExecute:update", operateType = LogOperateType.UPDATE, title = "更新所内就医-医嘱/处方执行记录",
    success = "更新所内就医-医嘱/处方执行记录成功", fail = "更新所内就医-医嘱/处方执行记录失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updatePrescribeExecute(@Valid @RequestBody PrescribeExecuteSaveReqVO updateReqVO) {
        prescribeExecuteService.updatePrescribeExecute(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除所内就医-医嘱/处方执行记录")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:prescribeExecute:delete", operateType = LogOperateType.DELETE, title = "删除所内就医-医嘱/处方执行记录",
    success = "删除所内就医-医嘱/处方执行记录成功", fail = "删除所内就医-医嘱/处方执行记录失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deletePrescribeExecute(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           prescribeExecuteService.deletePrescribeExecute(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得所内就医-医嘱/处方执行记录")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:prescribeExecute:get", operateType = LogOperateType.QUERY, title = "获取所内就医-医嘱/处方执行记录", bizNo = "{{#id}}", success = "获取所内就医-医嘱/处方执行记录成功", fail = "获取所内就医-医嘱/处方执行记录失败", extraInfo = "{{#id}}")
    public CommonResult<PrescribeExecuteRespVO> getPrescribeExecute(@RequestParam("id") String id) {
        PrescribeExecuteDO prescribeExecute = prescribeExecuteService.getPrescribeExecute(id);
        return success(BeanUtils.toBean(prescribeExecute, PrescribeExecuteRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得所内就医-医嘱/处方执行记录分页")
    @LogRecordAnnotation(bizModule = "ihc:prescribeExecute:page", operateType = LogOperateType.QUERY, title = "获得所内就医-医嘱/处方执行记录分页",
    success = "获得所内就医-医嘱/处方执行记录分页成功", fail = "获得所内就医-医嘱/处方执行记录分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<PrescribeExecuteRespVO>> getPrescribeExecutePage(@Valid @RequestBody PrescribeExecutePageReqVO pageReqVO) {
        PageResult<PrescribeExecuteDO> pageResult = prescribeExecuteService.getPrescribeExecutePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PrescribeExecuteRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得所内就医-医嘱/处方执行记录列表")
    @LogRecordAnnotation(bizModule = "ihc:prescribeExecute:list", operateType = LogOperateType.QUERY, title = "获得所内就医-医嘱/处方执行记录列表",
    success = "获得所内就医-医嘱/处方执行记录列表成功", fail = "获得所内就医-医嘱/处方执行记录列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<PrescribeExecuteRespVO>> getPrescribeExecuteList(@Valid @RequestBody PrescribeExecuteListReqVO listReqVO) {
    List<PrescribeExecuteDO> list = prescribeExecuteService.getPrescribeExecuteList(listReqVO);
        return success(BeanUtils.toBean(list, PrescribeExecuteRespVO.class));
    }

}
