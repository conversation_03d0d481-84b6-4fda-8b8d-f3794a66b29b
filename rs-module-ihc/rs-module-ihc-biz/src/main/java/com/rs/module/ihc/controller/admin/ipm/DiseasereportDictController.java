package com.rs.module.ihc.controller.admin.ipm;

import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.entity.ipm.DiseasereportDictDO;
import com.rs.module.ihc.service.ipm.DiseasereportDictService;

@Api(tags = "管理后台 - 所内就医-报病字典管理")
@RestController
@RequestMapping("/ihc/ipm/diseasereport-dict")
@Validated
@Log4j2
public class DiseasereportDictController {

    @Resource
    private DiseasereportDictService diseasereportDictService;

    /**
     * 创建所内就医-报病字典管理
     * @param createReqVO
     * @return
     */
    @PostMapping("/create")
    @ApiOperation(value = "创建所内就医-报病字典管理")
    @LogRecordAnnotation(bizModule = "ihc:diseasereportDict:create", operateType = LogOperateType.CREATE, title = "创建所内就医-报病字典管理",
    success = "创建所内就医-报病字典管理成功", fail = "创建所内就医-报病字典管理失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createDiseasereportDict(@Valid @RequestBody DiseasereportDictSaveReqVO createReqVO) {
        return success(diseasereportDictService.createDiseasereportDict(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新所内就医-报病字典管理")
    @LogRecordAnnotation(bizModule = "ihc:diseasereportDict:update", operateType = LogOperateType.UPDATE, title = "更新所内就医-报病字典管理",
    success = "更新所内就医-报病字典管理成功", fail = "更新所内就医-报病字典管理失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateDiseasereportDict(@Valid @RequestBody DiseasereportDictSaveReqVO updateReqVO) {
        diseasereportDictService.updateDiseasereportDict(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除所内就医-报病字典管理")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:diseasereportDict:delete", operateType = LogOperateType.DELETE, title = "删除所内就医-报病字典管理",
    success = "删除所内就医-报病字典管理成功", fail = "删除所内就医-报病字典管理失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteDiseasereportDict(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           diseasereportDictService.deleteDiseasereportDict(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得所内就医-报病字典管理")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:diseasereportDict:get", operateType = LogOperateType.QUERY, title = "获取所内就医-报病字典管理", bizNo = "{{#id}}", success = "获取所内就医-报病字典管理成功", fail = "获取所内就医-报病字典管理失败", extraInfo = "{{#id}}")
    public CommonResult<DiseasereportDictRespVO> getDiseasereportDict(@RequestParam("id") String id) {
        DiseasereportDictDO diseasereportDict = diseasereportDictService.getDiseasereportDict(id);
        return success(BeanUtils.toBean(diseasereportDict, DiseasereportDictRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得所内就医-报病字典管理分页")
    @LogRecordAnnotation(bizModule = "ihc:diseasereportDict:page", operateType = LogOperateType.QUERY, title = "获得所内就医-报病字典管理分页",
    success = "获得所内就医-报病字典管理分页成功", fail = "获得所内就医-报病字典管理分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<DiseasereportDictRespVO>> getDiseasereportDictPage(@Valid @RequestBody DiseasereportDictPageReqVO pageReqVO) {
        PageResult<DiseasereportDictDO> pageResult = diseasereportDictService.getDiseasereportDictPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DiseasereportDictRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得所内就医-报病字典管理列表")
    @LogRecordAnnotation(bizModule = "ihc:diseasereportDict:list", operateType = LogOperateType.QUERY, title = "获得所内就医-报病字典管理列表",
    success = "获得所内就医-报病字典管理列表成功", fail = "获得所内就医-报病字典管理列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<DiseasereportDictRespVO>> getDiseasereportDictList(@Valid @RequestBody DiseasereportDictListReqVO listReqVO) {
    List<DiseasereportDictDO> list = diseasereportDictService.getDiseasereportDictList(listReqVO);
        return success(BeanUtils.toBean(list, DiseasereportDictRespVO.class));
    }

    @GetMapping("/getDiseaseType")
    @ApiOperation(value = "获得所内就医-报病字典管理列表-字典")
    public CommonResult<List<DiseasereportDictTreeRespVO>> getDiseaseType() {
        List<DiseasereportDictDO> diseaseType = diseasereportDictService.getDiseaseType();
        return success(BeanUtils.toBean(diseaseType, DiseasereportDictTreeRespVO.class));
    }

}
