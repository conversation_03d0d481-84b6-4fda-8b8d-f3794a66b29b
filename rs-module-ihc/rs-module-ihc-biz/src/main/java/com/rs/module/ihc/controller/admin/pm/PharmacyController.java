package com.rs.module.ihc.controller.admin.pm;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.entity.pm.PharmacyDO;
import com.rs.module.ihc.service.pm.PharmacyService;

@Api(tags = "管理后台 - 药房管理-药房信息")
@RestController
@RequestMapping("/ihc/pm/pharmacy")
@Validated
public class PharmacyController {

    @Resource
    private PharmacyService pharmacyService;

    @PostMapping("/create")
    @ApiOperation(value = "创建药房管理-药房信息")
    @LogRecordAnnotation(bizModule = "ihc:pharmacy:create", operateType = LogOperateType.CREATE, title = "创建药房管理-药房信息",
    success = "创建药房管理-药房信息成功", fail = "创建药房管理-药房信息失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createPharmacy(@Valid @RequestBody PharmacySaveReqVO createReqVO) {
        return success(pharmacyService.createPharmacy(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新药房管理-药房信息")
    @LogRecordAnnotation(bizModule = "ihc:pharmacy:update", operateType = LogOperateType.UPDATE, title = "更新药房管理-药房信息",
    success = "更新药房管理-药房信息成功", fail = "更新药房管理-药房信息失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updatePharmacy(@Valid @RequestBody PharmacySaveReqVO updateReqVO) {
        pharmacyService.updatePharmacy(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除药房管理-药房信息")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:pharmacy:delete", operateType = LogOperateType.DELETE, title = "删除药房管理-药房信息",
    success = "删除药房管理-药房信息成功", fail = "删除药房管理-药房信息失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deletePharmacy(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           pharmacyService.deletePharmacy(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得药房管理-药房信息")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:pharmacy:get", operateType = LogOperateType.QUERY, title = "获取药房管理-药房信息", bizNo = "{{#id}}", success = "获取药房管理-药房信息成功", fail = "获取药房管理-药房信息失败", extraInfo = "{{#id}}")
    public CommonResult<PharmacyRespVO> getPharmacy(@RequestParam("id") String id) {
        PharmacyDO pharmacy = pharmacyService.getPharmacy(id);
        return success(BeanUtils.toBean(pharmacy, PharmacyRespVO.class));
    }

    @GetMapping("/page")
    @ApiOperation(value = "获得药房管理-药房信息分页")
    @LogRecordAnnotation(bizModule = "ihc:pharmacy:page", operateType = LogOperateType.QUERY, title = "获得药房管理-药房信息分页",
    success = "获得药房管理-药房信息分页成功", fail = "获得药房管理-药房信息分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<PharmacyRespVO>> getPharmacyPage(@Valid PharmacyPageReqVO pageReqVO) {
        PageResult<PharmacyDO> pageResult = pharmacyService.getPharmacyPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PharmacyRespVO.class));
    }

    @GetMapping("/list")
    @ApiOperation(value = "获得药房管理-药房信息列表")
    @LogRecordAnnotation(bizModule = "ihc:pharmacy:list", operateType = LogOperateType.QUERY, title = "获得药房管理-药房信息列表",
    success = "获得药房管理-药房信息列表成功", fail = "获得药房管理-药房信息列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<PharmacyRespVO>> getPharmacyList(@Valid PharmacyListReqVO listReqVO) {
    List<PharmacyDO> list = pharmacyService.getPharmacyList(listReqVO);
        return success(BeanUtils.toBean(list, PharmacyRespVO.class));
    }

}
