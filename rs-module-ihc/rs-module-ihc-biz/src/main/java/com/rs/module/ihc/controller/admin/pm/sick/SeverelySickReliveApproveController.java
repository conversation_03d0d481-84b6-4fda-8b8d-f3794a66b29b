package com.rs.module.ihc.controller.admin.pm.sick;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.pm.sick.vo.SeverelySickReliveApproveListReqVO;
import com.rs.module.ihc.controller.admin.pm.sick.vo.SeverelySickReliveApprovePageReqVO;
import com.rs.module.ihc.controller.admin.pm.sick.vo.SeverelySickReliveApproveRespVO;
import com.rs.module.ihc.controller.admin.pm.sick.vo.SeverelySickReliveApproveSaveReqVO;
import com.rs.module.ihc.entity.pm.sick.SeverelySickReliveApproveDO;
import com.rs.module.ihc.service.pm.sick.SeverelySickReliveApproveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "重特病号解除申请审批")
@RestController
@RequestMapping("/ihc/pm.sick/severelySickReliveApprove")
@Validated
public class SeverelySickReliveApproveController {

    @Resource
    private SeverelySickReliveApproveService severelySickReliveApproveService;

    @PostMapping("/create")
    @ApiOperation(value = "创建重特病号解除申请审批")
    public CommonResult<String> createSeverelySickReliveApprove(@Valid @RequestBody SeverelySickReliveApproveSaveReqVO createReqVO) {
        return success(severelySickReliveApproveService.createSeverelySickReliveApprove(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新重特病号解除申请审批")
    public CommonResult<Boolean> updateSeverelySickReliveApprove(@Valid @RequestBody SeverelySickReliveApproveSaveReqVO updateReqVO) {
        severelySickReliveApproveService.updateSeverelySickReliveApprove(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除重特病号解除申请审批")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteSeverelySickReliveApprove(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           severelySickReliveApproveService.deleteSeverelySickReliveApprove(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得重特病号解除申请审批")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<SeverelySickReliveApproveRespVO> getSeverelySickReliveApprove(@RequestParam("id") String id) {
        SeverelySickReliveApproveDO severelySickReliveApprove = severelySickReliveApproveService.getSeverelySickReliveApprove(id);
        return success(BeanUtils.toBean(severelySickReliveApprove, SeverelySickReliveApproveRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得重特病号解除申请审批分页")
    public CommonResult<PageResult<SeverelySickReliveApproveRespVO>> getSeverelySickReliveApprovePage(@Valid @RequestBody SeverelySickReliveApprovePageReqVO pageReqVO) {
        PageResult<SeverelySickReliveApproveDO> pageResult = severelySickReliveApproveService.getSeverelySickReliveApprovePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SeverelySickReliveApproveRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得重特病号解除申请审批列表")
    public CommonResult<List<SeverelySickReliveApproveRespVO>> getSeverelySickReliveApproveList(@Valid @RequestBody SeverelySickReliveApproveListReqVO listReqVO) {
        List<SeverelySickReliveApproveDO> list = severelySickReliveApproveService.getSeverelySickReliveApproveList(listReqVO);
        return success(BeanUtils.toBean(list, SeverelySickReliveApproveRespVO.class));
    }
}
