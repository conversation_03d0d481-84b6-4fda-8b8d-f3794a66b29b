package com.rs.module.ihc.config;

/**
 * @ClassName LiquibaseConfig
 * <AUTHOR>
 * @Date 2024/3/4 16:35
 * @Version 1.0
 */

import liquibase.integration.spring.SpringLiquibase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.DefaultResourceLoader;

import javax.annotation.Resource;
import javax.sql.DataSource;

/**
 * Liquibase配置类
 *
 * <AUTHOR> YangLinWei
 * @createTime: 2022/4/28 4:48 下午
 * @version: 1.0.0
 */
@Slf4j
@Configuration
public class LiquibaseConfig {

    /*** liquibase用到的两张表 **/
    private static final String DATABASE_CHANGE_LOG_TABLE = "ihc_lqb_changelog";
    private static final String DATABASE_CHANGE_LOG_LOCK_TABLE = "ihc_lqb_lock";

    @Resource
    private DataSource dataSource;

    /**
     * liquibase bean声明
     */
    @Bean
    public SpringLiquibase liquibase() {
        SpringLiquibase liquibase = new SpringLiquibase();
        liquibase.setClearCheckSums(true);
        liquibase.setChangeLog("classpath:db/master.xml");
        liquibase.setDataSource(dataSource);
        liquibase.setContexts("ihc");
        liquibase.setShouldRun(true);
        liquibase.setResourceLoader(new DefaultResourceLoader());
        liquibase.setDatabaseChangeLogTable(DATABASE_CHANGE_LOG_TABLE);
        liquibase.setDatabaseChangeLogLockTable(DATABASE_CHANGE_LOG_LOCK_TABLE);
        liquibase.setClearCheckSums(true);
        liquibase.setTestRollbackOnUpdate(false);
        return liquibase;
    }
}
