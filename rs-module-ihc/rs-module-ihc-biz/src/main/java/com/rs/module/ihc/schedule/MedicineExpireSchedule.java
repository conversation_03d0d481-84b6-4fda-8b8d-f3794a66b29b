package com.rs.module.ihc.schedule;

import com.rs.module.ihc.service.pm.MedicineInService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 医药药品过期定时任务
 */
@Slf4j
@Component
public class MedicineExpireSchedule implements ApplicationRunner {

    @Resource
    private MedicineInService ihcsMedicineInService;

    @Scheduled(cron = "0 0 0 * * ?")
    public void execute() {
        ihcsMedicineInService.selectExpireBatchAndUpdateMedicineFlag(null);
    }


    /**
     * 项目启动时执行一次
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        execute();
    }
}
