package com.rs.module.ihc.controller.app;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.RyReqVO;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.ihc.controller.admin.ephe.vo.RoomDisinfectRespVO;
import com.rs.module.ihc.controller.admin.ephe.vo.RoomDisinfectSaveReqVO;
import com.rs.module.ihc.controller.admin.ipm.appointment.dto.SaveMedicalAppointmentDTO;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.controller.admin.pm.prescribe.dto.PrescribeCheckupRegisterDTO;
import com.rs.module.ihc.controller.admin.pm.vo.MedicinePageReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineRespVO;
import com.rs.module.ihc.entity.ipm.CaseTemplateDO;
import com.rs.module.ihc.entity.ipm.VisitDO;
import com.rs.module.ihc.entity.pm.MedicineDO;
import com.rs.module.ihc.service.ephe.RoomDisinfectService;
import com.rs.module.ihc.service.ipm.*;
import com.rs.module.ihc.service.ipm.appointment.IhcsInternalMedicalAppointmentService;
import com.rs.module.ihc.service.ipm.dto.BatchExecutePrescribeDTO;
import com.rs.module.ihc.service.ipm.dto.ExecutePrescribeSignDTO;
import com.rs.module.ihc.service.ipm.dto.RefuseExecutePrescribeDTO;
import com.rs.module.ihc.service.pm.MedicineService;
import com.rs.module.ihc.service.pm.prescribe.InternalMedicalPrescribeCheckupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Validated
@RestController
@AllArgsConstructor
@RequestMapping("/app/ihc")
@Api(tags = "APP相关的接口")
public class IhcAppController {

    private final IhcsMedicalDeliveryRemindService ihcsMedicalDeliveryRemindService;
    private IhcsInternalMedicalAppointmentService sInternalMedicalAppointmentService;
    private MedicineService medicineService;
    private VisitService visitService;
    private PrescribeExecuteService prescribeExecuteService;
    private PrescribeService prescribeService;
    private PrisonerService prisonerService;
    private CaseTemplateService caseTemplateService;
    private OutpatientService outpatientService;
    private InternalMedicalPrescribeCheckupService internalMedicalPrescribeCheckupService;
    private RoomDisinfectService roomDisinfectService;


    /**
     * 监室确认送药
     *
     * @param batchExecutePrescribeDTO
     * @return
     */
    @PostMapping("/batchExecute")
    @ApiOperation("确认送药")
    public CommonResult<Void> batchExecutePrescribe(@RequestBody @Valid BatchExecutePrescribeDTO batchExecutePrescribeDTO) {
        ihcsMedicalDeliveryRemindService.batchExecutePrescribe(batchExecutePrescribeDTO);
        return CommonResult.success();
    }


    /**
     * 拒绝领药
     *
     * @param refuseExecutePrescribeDTO
     * @return
     */
    @PostMapping("/execute/refuse")
    @ApiOperation("拒绝领药")
    public CommonResult<Void> refuseExecutePrescribe(@RequestBody @Valid RefuseExecutePrescribeDTO refuseExecutePrescribeDTO) {
        ihcsMedicalDeliveryRemindService.refuseExecutePrescribe(refuseExecutePrescribeDTO);
        return CommonResult.success();
    }


    @PostMapping("/create")
    @ApiOperation(value = "新增在线报病")
    public CommonResult<String> createsInternalMedicalAppointment(@Valid @RequestBody SaveMedicalAppointmentDTO createReqVO) {
        return success(sInternalMedicalAppointmentService.createsInternalMedicalAppointment(createReqVO));
    }

    /**
     * 服药签名
     *
     * @param executePrescribeSignDTO
     * @return
     */
    @PostMapping("/sign")
    @ApiOperation("服药签名")
    public CommonResult<Void> sign(@RequestBody @Valid ExecutePrescribeSignDTO executePrescribeSignDTO) {
        ihcsMedicalDeliveryRemindService.executePrescribeSign(executePrescribeSignDTO);
        return CommonResult.success();
    }

    @GetMapping("/getMedicinePage")
    @ApiOperation(value = "选择药品")
    public CommonResult<PageResult<MedicineRespVO>> getMedicinePage(@Valid MedicinePageReqVO pageReqVO) {
        PageResult<MedicineDO> pageResult = medicineService.getMedicinePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MedicineRespVO.class));
    }

    @PostMapping("/createVisit")
    @ApiOperation(value = "创建现场巡检-现场巡检-现场司药")
    public CommonResult<String> createVisit(@Valid @RequestBody VisitSaveReqVO createReqVO) {
        createReqVO.setVisitProcessMethod("1");
        return success(visitService.createVisit(createReqVO));
    }

    @PostMapping("/create-xcxz-cf")
    @ApiOperation(value = "创建现场巡检-现场巡检-处方")
    public CommonResult<String> createVisitCf(@Valid @RequestBody VisitSaveCfReqVO visitSaveCfReqVO) {
        visitSaveCfReqVO.setVisitProcessMethod("2");
        return success(visitService.createVisitCf(visitSaveCfReqVO));
    }

    @PostMapping("/create-xcxz-wxyy")
    @ApiOperation(value = "创建现场巡检-现场巡检-无需用药")
    public CommonResult<String> createVisitWxcl(@Valid @RequestBody VisitSaveWxclReqVO visitSaveCfReqVO) {
        visitSaveCfReqVO.setVisitProcessMethod("3");
        return success(visitService.createVisitWxyy(visitSaveCfReqVO));
    }

    @PostMapping("/fyList")
    @ApiOperation(value = "服药列表")
    public CommonResult<List<PrescribeExecuteFytzRespVO>> fyList(@Valid @RequestBody PrescribeExecuteFytzReqVO prescribeExecuteListReqVO) {

        return success(prescribeExecuteService.fyList(prescribeExecuteListReqVO));
    }

    @GetMapping("/getRiskDisclosurePdf")
    @ApiOperation(value = "获取风险告知书")
    @ApiImplicitParam(name = "executionId", value = "执行记录id")
    public ResponseEntity<byte[]> getRiskDisclosurePdf(String executionId) {
        return CommonResult.fileStream(prescribeExecuteService.getRiskDisclosurePdf(executionId), "拒绝或放弃医学治疗风险告知书.pdf", MediaType.APPLICATION_PDF);
    }

    @PostMapping("/signRiskDisclosurePdf")
    @ApiOperation(value = "签风险告知书")
    @ApiImplicitParam(name = "executionId", value = "执行记录id")
    public CommonResult<?> signRiskDisclosurePdf(@RequestBody SignRiskDisclosurePdfVO signRiskDisclosurePdfVO) {
        prescribeExecuteService.signRiskDisclosurePdf(signRiskDisclosurePdfVO);
        return CommonResult.success();
    }

    @PostMapping("/getPrescribeList")
    @ApiOperation(value = "送药提醒")
    @ApiImplicitParam(name = "roomId", value = "监室id")
    public CommonResult<List<PrescribeRespVO>> getPrescribeList(String roomId) {
        return success(prescribeService.getPrescribeList(roomId));
    }

    @PostMapping("/selectFxgzsTodo")
    @ApiOperation(value = "获得待签的风险告知书")
    public CommonResult<List<PrescribeExecuteFytzRespVO>> selectFxgzsTodo(@Valid @RequestBody PrescribeExecuteFxgzTodoReqVO listReqVO) {
        return success(prescribeExecuteService.selectFxgzsTodo(listReqVO));
    }

    @PostMapping("/selectFxgzsSuccess")
    @ApiOperation(value = "获得已签的风险告知书")
    public CommonResult<List<PrescribeExecuteFytzRespVO>> selectFxgzsSuccess(@Valid @RequestBody PrescribeExecuteFxgzTodoReqVO listReqVO) {
        return success(prescribeExecuteService.selectFxgzsSuccess(listReqVO));
    }


    @GetMapping("/getPrescribeListByJgrybm")
    @ApiOperation(value = "根据被监管人员返回医嘱")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "jgrybm", value = "监管人员编码，用于唯一标识被监管人员"),
            @ApiImplicitParam(name = "doctorAdviceType", value = "医嘱类型：1表示临时医嘱、2表示长期医嘱")
    })
    public CommonResult<List<PrescribeRespVO>> getPrescribeListByJgrybm(String jgrybm, String doctorAdviceType) {
        return success(prescribeService.getPrescribeListByJgrybm(jgrybm, doctorAdviceType));
    }


    @GetMapping("/getPrisonerList")
    @ApiOperation(value = "根据监室号查询人员信息")
    public CommonResult<List<PrisonerVwRespVO>> getPrisonerByJsh(@RequestParam("roomId") String roomId) {
        return success(prisonerService.getPrisonerListByJsh(roomId));
    }

    @PostMapping("/getCaseTemplatelist")
    @ApiOperation(value = "获得所内就医-病例模板列表")
    public CommonResult<List<CaseTemplateRespVO>> getCaseTemplateList(@Valid @RequestBody CaseTemplateListReqVO listReqVO) {
        List<CaseTemplateDO> list = caseTemplateService.getCaseTemplateList(listReqVO);
        return success(BeanUtils.toBean(list, CaseTemplateRespVO.class));
    }

    @GetMapping("/getVisitTodoByJgrybm")
    @ApiOperation(value = "根据监管人员编码获取巡诊待办数据")
    @ApiImplicitParam(name = "jgrybm", value = "监管人员编码")
    public CommonResult<List<VisitRespVO>> getVisitTodoByJgrybm(String jgrybm) {
        RyReqVO ry = new RyReqVO();
        ry.setJgrybm(jgrybm);
        return success(visitService.getVisitTodo(ry));
    }


    @GetMapping("/visit/listXzjgByRoomId")
    @ApiOperation(value = "根据类型获取监室的巡诊结果列表")
    @ApiImplicitParam(name = "type", value = "1现场司药2开具处方3无需用药")
    public CommonResult<List<VisitXzjgRespVO>> listXzjgByRoomId(String type, String roomId) {
        return success(visitService.listXzjgByRoomId(type, roomId));
    }

    @GetMapping("/visit/get-cf")
    @ApiOperation(value = "获得现场巡诊-现场巡诊-处方详情")
    @ApiImplicitParam(name = "id", value = "处方id")
    public CommonResult<VisitPrescribeRespVO> getCf(@RequestParam("id") String id) {
        VisitDO visitDO = visitService.getVisit(id);
        return success(BeanUtils.toBean(visitDO, VisitPrescribeRespVO.class));
    }

    @GetMapping("/visit/get")
    @ApiOperation(value = "获得现场巡诊-现场巡诊-现场司药或无需用药详情")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<VisitRespVO> getVisit(@RequestParam("id") String id) {
        VisitDO visit = visitService.getVisit(id);
        return success(BeanUtils.toBean(visit, VisitRespVO.class));
    }

    @ApiOperation(value = "生效医嘱-检查登记")
    @PostMapping("/advice/register")
    public CommonResult<Void> prescribeCheckupRegister(@RequestBody @Valid PrescribeCheckupRegisterDTO prescribeCheckupRegisterDTO) {
        internalMedicalPrescribeCheckupService.prescribeCheckupRegister(prescribeCheckupRegisterDTO);
        return CommonResult.success();
    }

    @PostMapping("/addRoomDisinfect")
    @ApiOperation(value = "创建监室消毒")
    public CommonResult<String> addRoomDisinfect(@Valid @RequestBody RoomDisinfectSaveReqVO createReqVO) {
        return success(roomDisinfectService.createRoomDisinfect(createReqVO));
    }

    @PostMapping("/listRoomDisinfect")
    @ApiOperation(value = "获取监室消毒列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "timeType", value = "时间段：1 全部，2 今天，3 昨天，4 近一周", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "roomId", value = "监室号", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "orgCode", value = "机构编号", dataType = "String", paramType = "query", required = true)
    })
    public CommonResult<List<RoomDisinfectRespVO>> listRoomDisinfect(@RequestParam(value = "timeType") String timeType,
                                                                     @RequestParam("roomId") String roomId,
                                                                     @RequestParam("orgCode") String orgCode) {
        List<RoomDisinfectRespVO> list = roomDisinfectService.listRoomDisinfect(timeType, roomId, orgCode);
        return success(list);
    }
}
