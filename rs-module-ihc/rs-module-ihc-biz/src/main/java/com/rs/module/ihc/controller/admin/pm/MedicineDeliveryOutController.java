package com.rs.module.ihc.controller.admin.pm;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.entity.pm.MedicineDeliveryOutDO;
import com.rs.module.ihc.service.pm.MedicineDeliveryOutService;

@Api(tags = "管理后台 - 药房管理-顾送药品出库")
@RestController
@RequestMapping("/ihc/pm/medicineDeliveryOut")
@Validated
public class MedicineDeliveryOutController {

    @Resource
    private MedicineDeliveryOutService medicineDeliveryOutService;

    @PostMapping("/create")
    @ApiOperation(value = "创建药房管理-顾送药品出库")
    @LogRecordAnnotation(bizModule = "ihc:medicineDeliveryOut:create", operateType = LogOperateType.CREATE, title = "创建药房管理-顾送药品出库",
    success = "创建药房管理-顾送药品出库成功", fail = "创建药房管理-顾送药品出库失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createMedicineDeliveryOut(@Valid @RequestBody MedicineDeliveryOutSaveReqVO createReqVO) {
        return success(medicineDeliveryOutService.createMedicineDeliveryOut(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新药房管理-顾送药品出库")
    @LogRecordAnnotation(bizModule = "ihc:medicineDeliveryOut:update", operateType = LogOperateType.UPDATE, title = "更新药房管理-顾送药品出库",
    success = "更新药房管理-顾送药品出库成功", fail = "更新药房管理-顾送药品出库失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateMedicineDeliveryOut(@Valid @RequestBody MedicineDeliveryOutSaveReqVO updateReqVO) {
        medicineDeliveryOutService.updateMedicineDeliveryOut(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除药房管理-顾送药品出库")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:medicineDeliveryOut:delete", operateType = LogOperateType.DELETE, title = "删除药房管理-顾送药品出库",
    success = "删除药房管理-顾送药品出库成功", fail = "删除药房管理-顾送药品出库失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteMedicineDeliveryOut(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           medicineDeliveryOutService.deleteMedicineDeliveryOut(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得药房管理-顾送药品出库")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:medicineDeliveryOut:get", operateType = LogOperateType.QUERY, title = "获取药房管理-顾送药品出库", bizNo = "{{#id}}", success = "获取药房管理-顾送药品出库成功", fail = "获取药房管理-顾送药品出库失败", extraInfo = "{{#id}}")
    public CommonResult<MedicineDeliveryOutRespVO> getMedicineDeliveryOut(@RequestParam("id") String id) {
        MedicineDeliveryOutDO medicineDeliveryOut = medicineDeliveryOutService.getMedicineDeliveryOut(id);
        return success(BeanUtils.toBean(medicineDeliveryOut, MedicineDeliveryOutRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得药房管理-顾送药品出库分页")
    @LogRecordAnnotation(bizModule = "ihc:medicineDeliveryOut:page", operateType = LogOperateType.QUERY, title = "获得药房管理-顾送药品出库分页",
    success = "获得药房管理-顾送药品出库分页成功", fail = "获得药房管理-顾送药品出库分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<MedicineDeliveryOutRespVO>> getMedicineDeliveryOutPage(@Valid @RequestBody MedicineDeliveryOutPageReqVO pageReqVO) {
        PageResult<MedicineDeliveryOutDO> pageResult = medicineDeliveryOutService.getMedicineDeliveryOutPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MedicineDeliveryOutRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得药房管理-顾送药品出库列表")
    @LogRecordAnnotation(bizModule = "ihc:medicineDeliveryOut:list", operateType = LogOperateType.QUERY, title = "获得药房管理-顾送药品出库列表",
    success = "获得药房管理-顾送药品出库列表成功", fail = "获得药房管理-顾送药品出库列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<MedicineDeliveryOutRespVO>> getMedicineDeliveryOutList(@Valid @RequestBody MedicineDeliveryOutListReqVO listReqVO) {
    List<MedicineDeliveryOutDO> list = medicineDeliveryOutService.getMedicineDeliveryOutList(listReqVO);
        return success(BeanUtils.toBean(list, MedicineDeliveryOutRespVO.class));
    }

}
