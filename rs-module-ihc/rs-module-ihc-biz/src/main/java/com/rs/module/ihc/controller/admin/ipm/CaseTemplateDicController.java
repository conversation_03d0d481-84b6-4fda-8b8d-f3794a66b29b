package com.rs.module.ihc.controller.admin.ipm;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.handler.ChainBuilder;
import com.rs.framework.common.handler.Handler;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.entity.ipm.CaseTemplateDicDO;
import com.rs.module.ihc.handler.ipm.casetl.dic.*;
import com.rs.module.ihc.service.ipm.CaseTemplateDicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 所内就医-病例模板表-字典")
@RestController
@RequestMapping("/ihc/ipm/case-template-dic")
@Validated
public class CaseTemplateDicController {

    @Resource
    private CaseTemplateDicService caseTemplateDicService;

    @Autowired
    private ChainBuilder<CaseTemplateDicRespV2VO> chainBuilder;

    @Autowired
    private CnrsgHandler cnrsgHandler;
    @Autowired
    private CnrtzHandler cnrtzHandler;

    @Autowired
    private EtsgHandler etsgHandler;

    @Autowired
    private GrsHandler grsHandler;

    @Autowired
    private HxHandler hxHandler;

    @Autowired
    private JwsHandler jwsHandler;

    @Autowired
    private JwyHandler jwyHandler;

    @Autowired
    private MbHandler mbHandler;

    @Autowired
    private QsnHandler qsnHandler;

    @Autowired
    private QsnsgHandler qsnsgHandler;

    @Autowired
    private SsyHandler ssyHandler;

    @Autowired
    private SzyHandler szyHandler;

    @Autowired
    private TwHandler twHandler;

    @Autowired
    private TzHandler tzHandler;

    @Autowired
    private XtHandler xtHandler;

    @Autowired
    private XyHandler xyHandler;

    @Autowired
    private YetzHandler yetzHandler;

    @Autowired
    private ZszqHandler zszqHandler;

    @Autowired
    private ZsHandler zsHandler;

    @PostMapping("/create")
    @ApiOperation(value = "创建所内就医-病例模板表-字典")
    @LogRecordAnnotation(bizModule = "ihc:caseTemplateDic:create", operateType = LogOperateType.CREATE, title = "创建所内就医-病例模板表-字典",
            success = "创建所内就医-病例模板表-字典成功", fail = "创建所内就医-病例模板表-字典失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createCaseTemplateDic(@Valid @RequestBody CaseTemplateDicSaveReqVO createReqVO) {
        return success(caseTemplateDicService.createCaseTemplateDic(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新所内就医-病例模板表-字典")
    @LogRecordAnnotation(bizModule = "ihc:caseTemplateDic:update", operateType = LogOperateType.UPDATE, title = "更新所内就医-病例模板表-字典",
            success = "更新所内就医-病例模板表-字典成功", fail = "更新所内就医-病例模板表-字典失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateCaseTemplateDic(@Valid @RequestBody CaseTemplateDicSaveReqVO updateReqVO) {
        caseTemplateDicService.updateCaseTemplateDic(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除所内就医-病例模板表-字典")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:caseTemplateDic:delete", operateType = LogOperateType.DELETE, title = "删除所内就医-病例模板表-字典",
            success = "删除所内就医-病例模板表-字典成功", fail = "删除所内就医-病例模板表-字典失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteCaseTemplateDic(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            caseTemplateDicService.deleteCaseTemplateDic(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得所内就医-病例模板表-字典")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:caseTemplateDic:get", operateType = LogOperateType.QUERY, title = "获取所内就医-病例模板表-字典", bizNo = "{{#id}}", success = "获取所内就医-病例模板表-字典成功", fail = "获取所内就医-病例模板表-字典失败", extraInfo = "{{#id}}")
    public CommonResult<CaseTemplateDicRespVO> getCaseTemplateDic(@RequestParam("id") String id) {
        CaseTemplateDicDO caseTemplateDic = caseTemplateDicService.getCaseTemplateDic(id);
        return success(BeanUtils.toBean(caseTemplateDic, CaseTemplateDicRespVO.class));
    }

    @GetMapping("/page")
    @ApiOperation(value = "获得所内就医-病例模板表-字典分页")
    @LogRecordAnnotation(bizModule = "ihc:caseTemplateDic:page", operateType = LogOperateType.QUERY, title = "获得所内就医-病例模板表-字典分页",
            success = "获得所内就医-病例模板表-字典分页成功", fail = "获得所内就医-病例模板表-字典分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<CaseTemplateDicRespVO>> getCaseTemplateDicPage(@Valid CaseTemplateDicPageReqVO pageReqVO) {
        PageResult<CaseTemplateDicDO> pageResult = caseTemplateDicService.getCaseTemplateDicPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CaseTemplateDicRespVO.class));
    }

    @GetMapping("/list")
    @ApiOperation(value = "获得所内就医-病例模板表-字典列表")
    @LogRecordAnnotation(bizModule = "ihc:caseTemplateDic:list", operateType = LogOperateType.QUERY, title = "获得所内就医-病例模板表-字典列表",
            success = "获得所内就医-病例模板表-字典列表成功", fail = "获得所内就医-病例模板表-字典列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<CaseTemplateDicRespVO>> getCaseTemplateDicList(@Valid CaseTemplateDicListReqVO listReqVO) {
        List<CaseTemplateDicDO> list = caseTemplateDicService.getCaseTemplateDicList(listReqVO);
        return success(BeanUtils.toBean(list, CaseTemplateDicRespVO.class));
    }

    @GetMapping("/v2/list")
    @ApiOperation(value = "获得所内就医-病例模板表-字典列表")
    @LogRecordAnnotation(bizModule = "ihc:caseTemplateDic:list", operateType = LogOperateType.QUERY, title = "获得所内就医-病例模板表-字典列表",
            success = "获得所内就医-病例模板表-字典列表成功", fail = "获得所内就医-病例模板表-字典列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<CaseTemplateDicRespV2VO> getCaseTemplateDicListV2() {
        CaseTemplateDicRespV2VO medicineInImportListVO = new CaseTemplateDicRespV2VO();
        Handler<CaseTemplateDicRespV2VO> chain = chainBuilder.buildChain(Arrays.asList(
                cnrsgHandler,
                cnrtzHandler,
                etsgHandler,
                grsHandler,
                hxHandler,
                jwsHandler,
                jwyHandler,
                mbHandler,
                qsnHandler,
                qsnsgHandler,
                ssyHandler,
                szyHandler,
                twHandler,
                tzHandler,
                xtHandler,
                xyHandler,
                yetzHandler,
                zszqHandler,
                zsHandler
        ));
        CaseTemplateDicRespV2VO handleResult = chain.handle(medicineInImportListVO);
        return success(handleResult);
    }

}
