package com.rs.module.ihc.controller.admin.pm.prescribe;


import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.ipm.vo.InternalMedicalRemotePrescribeVO;
import com.rs.module.ihc.controller.admin.ipm.vo.RemotePrescribeCfRespVO;
import com.rs.module.ihc.entity.ipm.PrescribeDO;
import com.rs.module.ihc.entity.ipm.appointment.IhcsInternalMedicalAppointmentDO;
import com.rs.module.ihc.service.ipm.PrescribeService;
import com.rs.module.ihc.service.remote.IhcsInternalMedicalRemoteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Validated
@RestController
@AllArgsConstructor
@RequestMapping("/internal/medical/remote")
@Api(tags = "所内就医-远程问诊")
public class IhcsInternalMedicalRemoteController {

    private final IhcsInternalMedicalRemoteService ihcsInternalMedicalRemoteService;
    private final PrescribeService prescribeService;


    @PostMapping("/prescribe")
    @ApiOperation("远程问诊开具处方")
    public CommonResult<Void> internalMedicalRemotePrescribe(@RequestBody @Valid InternalMedicalRemotePrescribeVO internalMedicalRemotePrescribeDTO) {
        ihcsInternalMedicalRemoteService.internalMedicalRemotePrescribe(internalMedicalRemotePrescribeDTO);
        return CommonResult.success();
    }

    @GetMapping("/getByPrescribeId")
    @ApiOperation("获取远程问诊处方详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "prescribeId", value = "处方id", dataType = "int", paramType = "query", required = true)
    })
    public CommonResult<RemotePrescribeCfRespVO> getRemotePrescribeById(@RequestParam @NotNull(message = "处方id不能为空") String prescribeId) {
        PrescribeDO prescribe = prescribeService.getPrescribe(prescribeId);
        IhcsInternalMedicalAppointmentDO appointment = ihcsInternalMedicalRemoteService.getIhcsInternalMedicalAppointmentById(prescribe.getBusinessId());
        RemotePrescribeCfRespVO remotePrescribeCfRespVO = null;
        if (appointment != null) {
            remotePrescribeCfRespVO = BeanUtils.toBean(appointment, RemotePrescribeCfRespVO.class);
        } else {
            remotePrescribeCfRespVO = new RemotePrescribeCfRespVO();
        }
        remotePrescribeCfRespVO.setCfid(prescribe.getId());
        return CommonResult.success(remotePrescribeCfRespVO);
    }

}
