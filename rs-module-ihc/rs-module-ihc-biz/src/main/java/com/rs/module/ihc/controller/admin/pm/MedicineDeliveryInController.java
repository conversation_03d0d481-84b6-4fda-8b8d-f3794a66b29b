package com.rs.module.ihc.controller.admin.pm;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.entity.pm.MedicineDeliveryInDO;
import com.rs.module.ihc.service.pm.MedicineDeliveryInService;

@Api(tags = "管理后台 - 药房管理-顾送药品入库批次")
@RestController
@RequestMapping("/ihc/pm/medicine-delivery-in")
@Validated
public class MedicineDeliveryInController {

    @Resource
    private MedicineDeliveryInService medicineDeliveryInService;

    @PostMapping("/create")
    @ApiOperation(value = "创建药房管理-顾送药品入库批次")
    @LogRecordAnnotation(bizModule = "ihc:medicineDeliveryIn:create", operateType = LogOperateType.CREATE, title = "创建药房管理-顾送药品入库批次",
    success = "创建药房管理-顾送药品入库批次成功", fail = "创建药房管理-顾送药品入库批次失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createMedicineDeliveryIn(@Valid @RequestBody MedicineDeliveryInSaveReqVO createReqVO) {
        return success(medicineDeliveryInService.createMedicineDeliveryIn(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新药房管理-顾送药品入库批次")
    @LogRecordAnnotation(bizModule = "ihc:medicineDeliveryIn:update", operateType = LogOperateType.UPDATE, title = "更新药房管理-顾送药品入库批次",
    success = "更新药房管理-顾送药品入库批次成功", fail = "更新药房管理-顾送药品入库批次失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateMedicineDeliveryIn(@Valid @RequestBody MedicineDeliveryInSaveReqVO updateReqVO) {
        medicineDeliveryInService.updateMedicineDeliveryIn(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除药房管理-顾送药品入库批次")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:medicineDeliveryIn:delete", operateType = LogOperateType.DELETE, title = "删除药房管理-顾送药品入库批次",
    success = "删除药房管理-顾送药品入库批次成功", fail = "删除药房管理-顾送药品入库批次失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteMedicineDeliveryIn(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           medicineDeliveryInService.deleteMedicineDeliveryIn(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得药房管理-顾送药品入库批次")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:medicineDeliveryIn:get", operateType = LogOperateType.QUERY, title = "获取药房管理-顾送药品入库批次", bizNo = "{{#id}}", success = "获取药房管理-顾送药品入库批次成功", fail = "获取药房管理-顾送药品入库批次失败", extraInfo = "{{#id}}")
    public CommonResult<MedicineDeliveryInRespVO> getMedicineDeliveryIn(@RequestParam("id") String id) {
        MedicineDeliveryInDO medicineDeliveryIn = medicineDeliveryInService.getMedicineDeliveryIn(id);
        return success(BeanUtils.toBean(medicineDeliveryIn, MedicineDeliveryInRespVO.class));
    }

    @GetMapping("/page")
    @ApiOperation(value = "获得药房管理-顾送药品入库批次分页")
    @LogRecordAnnotation(bizModule = "ihc:medicineDeliveryIn:page", operateType = LogOperateType.QUERY, title = "获得药房管理-顾送药品入库批次分页",
    success = "获得药房管理-顾送药品入库批次分页成功", fail = "获得药房管理-顾送药品入库批次分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<MedicineDeliveryInRespVO>> getMedicineDeliveryInPage(@Valid MedicineDeliveryInPageReqVO pageReqVO) {
        PageResult<MedicineDeliveryInDO> pageResult = medicineDeliveryInService.getMedicineDeliveryInPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MedicineDeliveryInRespVO.class));
    }

    @GetMapping("/list")
    @ApiOperation(value = "获得药房管理-顾送药品入库批次列表")
    @LogRecordAnnotation(bizModule = "ihc:medicineDeliveryIn:list", operateType = LogOperateType.QUERY, title = "获得药房管理-顾送药品入库批次列表",
    success = "获得药房管理-顾送药品入库批次列表成功", fail = "获得药房管理-顾送药品入库批次列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<MedicineDeliveryInRespVO>> getMedicineDeliveryInList(@Valid MedicineDeliveryInListReqVO listReqVO) {
    List<MedicineDeliveryInDO> list = medicineDeliveryInService.getMedicineDeliveryInList(listReqVO);
        return success(BeanUtils.toBean(list, MedicineDeliveryInRespVO.class));
    }

}
