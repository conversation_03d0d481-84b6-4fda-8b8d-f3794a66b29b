package com.rs.module.ihc.controller.admin.idm;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.idm.vo.InfectionRespVO;
import com.rs.module.ihc.controller.admin.idm.vo.InfectionSaveReqVO;
import com.rs.module.ihc.entity.idm.InfectionDO;
import com.rs.module.ihc.service.idm.InfectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "传染病管理-传染病管理登记")
@RestController
@RequestMapping("/ihc/idm/infection")
@Validated
public class InfectionController {

    @Resource
    private InfectionService infectionService;

    @PostMapping("/create")
    @ApiOperation(value = "传染病管理登记")
    @LogRecordAnnotation(bizModule = "ihc:idm:infection:create", operateType = LogOperateType.CREATE, title = "传染病管理-传染病管理登记",
            success = "传染病管理-传染病管理登记-成功", fail = "传染病管理-传染病管理，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createInfection(@Valid @RequestBody InfectionSaveReqVO createReqVO) {
        return success(infectionService.createInfection(createReqVO));
    }


    @GetMapping("/get")
    @ApiOperation(value = "获得传染病管理-传染病管理登记")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:idm:infection-get", operateType = LogOperateType.QUERY, title = "获得传染病管理-传染病管理登记",
            success = "传染病管理-定期检查登记-成功", fail = "获得传染病管理-传染病管理登记-错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    public CommonResult<InfectionRespVO> getInfection(@RequestParam("id") String id) {
        InfectionDO infection = infectionService.getInfection(id);
        return success(BeanUtils.toBean(infection, InfectionRespVO.class));
    }


}
