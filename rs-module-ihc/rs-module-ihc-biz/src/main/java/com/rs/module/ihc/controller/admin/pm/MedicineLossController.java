package com.rs.module.ihc.controller.admin.pm;

import cn.hutool.core.util.ObjectUtil;
import com.rs.module.ihc.controller.admin.pm.dto.BatchSaveMedicineLossDTO;
import com.rs.module.ihc.service.pm.MedicineInService;
import com.rs.module.ihc.service.pm.MedicineService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.entity.pm.MedicineLossDO;
import com.rs.module.ihc.service.pm.MedicineLossService;

@Api(tags = "管理后台 - 药房管理-药品报损")
@RestController
@RequestMapping("/ihc/pm/medicine-loss")
@Validated
public class MedicineLossController {

    @Resource
    private MedicineLossService medicineLossService;
    @Resource
    private MedicineInService medicineInService;
    @Resource
    private MedicineService medicineService;

    @PostMapping("/create")
    @ApiOperation(value = "创建药房管理-药品报损")
    @LogRecordAnnotation(bizModule = "ihc:medicineLoss:create", operateType = LogOperateType.CREATE, title = "创建药房管理-药品报损",
    success = "创建药房管理-药品报损成功", fail = "创建药房管理-药品报损失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createMedicineLoss(@Valid @RequestBody MedicineLossSaveReqVO createReqVO) {
        return success(medicineLossService.createMedicineLoss(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新药房管理-药品报损")
    @LogRecordAnnotation(bizModule = "ihc:medicineLoss:update", operateType = LogOperateType.UPDATE, title = "更新药房管理-药品报损",
    success = "更新药房管理-药品报损成功", fail = "更新药房管理-药品报损失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateMedicineLoss(@Valid @RequestBody MedicineLossSaveReqVO updateReqVO) {
        medicineLossService.updateMedicineLoss(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除药房管理-药品报损")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:medicineLoss:delete", operateType = LogOperateType.DELETE, title = "删除药房管理-药品报损",
    success = "删除药房管理-药品报损成功", fail = "删除药房管理-药品报损失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteMedicineLoss(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           medicineLossService.deleteMedicineLoss(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得药房管理-药品报损")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:medicineLoss:get", operateType = LogOperateType.QUERY, title = "获取药房管理-药品报损", bizNo = "{{#id}}", success = "获取药房管理-药品报损成功", fail = "获取药房管理-药品报损失败", extraInfo = "{{#id}}")
    public CommonResult<MedicineLossRespVO> getMedicineLoss(@RequestParam("id") String id) {
        MedicineLossDO medicineLoss = medicineLossService.getMedicineLoss(id);
        return success(BeanUtils.toBean(medicineLoss, MedicineLossRespVO.class));
    }

    @GetMapping("/getDetail")
    @ApiOperation(value = "获得药房管理-药品报损详情")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:medicineLoss:get", operateType = LogOperateType.QUERY, title = "获取药房管理-药品报损详情", bizNo = "{{#id}}", success = "获取药房管理-药品报损详情成功", fail = "获取药房管理-药品报损详情失败", extraInfo = "{{#id}}")
    public CommonResult<MedicineLossRespVO> getMedicineLossDetail(@RequestParam("id") String id) {
        MedicineLossDO medicineLoss = medicineLossService.getMedicineLoss(id);
        if (ObjectUtil.isEmpty(medicineLoss)) {
            return success(new MedicineLossRespVO());
        }
        MedicineLossRespVO lossRespVO = BeanUtils.toBean(medicineLoss, MedicineLossRespVO.class);
        lossRespVO.setMedicineIn(medicineInService.getMedicineInDetail(medicineLoss.getMedicineInId()));
        lossRespVO.setMedicine(medicineService.getMedicineDetail(medicineLoss.getMedicineId()));
        return success(lossRespVO);
    }

    @GetMapping("/page")
    @ApiOperation(value = "获得药房管理-药品报损分页")
    @LogRecordAnnotation(bizModule = "ihc:medicineLoss:page", operateType = LogOperateType.QUERY, title = "获得药房管理-药品报损分页",
    success = "获得药房管理-药品报损分页成功", fail = "获得药房管理-药品报损分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<MedicineLossRespVO>> getMedicineLossPage(@Valid MedicineLossPageReqVO pageReqVO) {
        PageResult<MedicineLossDO> pageResult = medicineLossService.getMedicineLossPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MedicineLossRespVO.class));
    }

    @GetMapping("/list")
    @ApiOperation(value = "获得药房管理-药品报损列表")
    @LogRecordAnnotation(bizModule = "ihc:medicineLoss:list", operateType = LogOperateType.QUERY, title = "获得药房管理-药品报损列表",
    success = "获得药房管理-药品报损列表成功", fail = "获得药房管理-药品报损列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<MedicineLossRespVO>> getMedicineLossList(@Valid MedicineLossListReqVO listReqVO) {
    List<MedicineLossDO> list = medicineLossService.getMedicineLossList(listReqVO);
        return success(BeanUtils.toBean(list, MedicineLossRespVO.class));
    }

    @PostMapping("/batchSave")
    @ApiOperation("批量新增报损批次")
    public CommonResult<Void> batchSaveMedicineLoss(@RequestBody @Valid BatchSaveMedicineLossDTO batchSaveMedicineLossDTO) {
        medicineLossService.batchSaveMedicineLoss(batchSaveMedicineLossDTO.getMedicineLossList(), batchSaveMedicineLossDTO.getId());
        return CommonResult.success();
    }

}
