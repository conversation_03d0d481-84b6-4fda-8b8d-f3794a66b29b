package com.rs.module.ihc.controller.admin.pm;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.entity.pm.MedicineDeliveryLossDO;
import com.rs.module.ihc.service.pm.MedicineDeliveryLossService;

@Api(tags = "管理后台 - 药房管理-顾送药品报损")
@RestController
@RequestMapping("/ihc/pm/medicine-delivery-loss")
@Validated
public class MedicineDeliveryLossController {

    @Resource
    private MedicineDeliveryLossService medicineDeliveryLossService;

    @PostMapping("/create")
    @ApiOperation(value = "创建药房管理-顾送药品报损")
    @LogRecordAnnotation(bizModule = "ihc:medicineDeliveryLoss:create", operateType = LogOperateType.CREATE, title = "创建药房管理-顾送药品报损",
    success = "创建药房管理-顾送药品报损成功", fail = "创建药房管理-顾送药品报损失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createMedicineDeliveryLoss(@Valid @RequestBody MedicineDeliveryLossSaveReqVO createReqVO) {
        return success(medicineDeliveryLossService.createMedicineDeliveryLoss(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新药房管理-顾送药品报损")
    @LogRecordAnnotation(bizModule = "ihc:medicineDeliveryLoss:update", operateType = LogOperateType.UPDATE, title = "更新药房管理-顾送药品报损",
    success = "更新药房管理-顾送药品报损成功", fail = "更新药房管理-顾送药品报损失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateMedicineDeliveryLoss(@Valid @RequestBody MedicineDeliveryLossSaveReqVO updateReqVO) {
        medicineDeliveryLossService.updateMedicineDeliveryLoss(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除药房管理-顾送药品报损")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:medicineDeliveryLoss:delete", operateType = LogOperateType.DELETE, title = "删除药房管理-顾送药品报损",
    success = "删除药房管理-顾送药品报损成功", fail = "删除药房管理-顾送药品报损失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteMedicineDeliveryLoss(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           medicineDeliveryLossService.deleteMedicineDeliveryLoss(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得药房管理-顾送药品报损")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:medicineDeliveryLoss:get", operateType = LogOperateType.QUERY, title = "获取药房管理-顾送药品报损", bizNo = "{{#id}}", success = "获取药房管理-顾送药品报损成功", fail = "获取药房管理-顾送药品报损失败", extraInfo = "{{#id}}")
    public CommonResult<MedicineDeliveryLossRespVO> getMedicineDeliveryLoss(@RequestParam("id") String id) {
        MedicineDeliveryLossDO medicineDeliveryLoss = medicineDeliveryLossService.getMedicineDeliveryLoss(id);
        return success(BeanUtils.toBean(medicineDeliveryLoss, MedicineDeliveryLossRespVO.class));
    }

    @GetMapping("/page")
    @ApiOperation(value = "获得药房管理-顾送药品报损分页")
    @LogRecordAnnotation(bizModule = "ihc:medicineDeliveryLoss:page", operateType = LogOperateType.QUERY, title = "获得药房管理-顾送药品报损分页",
    success = "获得药房管理-顾送药品报损分页成功", fail = "获得药房管理-顾送药品报损分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<MedicineDeliveryLossRespVO>> getMedicineDeliveryLossPage(@Valid MedicineDeliveryLossPageReqVO pageReqVO) {
        PageResult<MedicineDeliveryLossDO> pageResult = medicineDeliveryLossService.getMedicineDeliveryLossPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MedicineDeliveryLossRespVO.class));
    }

    @GetMapping("/list")
    @ApiOperation(value = "获得药房管理-顾送药品报损列表")
    @LogRecordAnnotation(bizModule = "ihc:medicineDeliveryLoss:list", operateType = LogOperateType.QUERY, title = "获得药房管理-顾送药品报损列表",
    success = "获得药房管理-顾送药品报损列表成功", fail = "获得药房管理-顾送药品报损列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<MedicineDeliveryLossRespVO>> getMedicineDeliveryLossList(@Valid MedicineDeliveryLossListReqVO listReqVO) {
    List<MedicineDeliveryLossDO> list = medicineDeliveryLossService.getMedicineDeliveryLossList(listReqVO);
        return success(BeanUtils.toBean(list, MedicineDeliveryLossRespVO.class));
    }

}
