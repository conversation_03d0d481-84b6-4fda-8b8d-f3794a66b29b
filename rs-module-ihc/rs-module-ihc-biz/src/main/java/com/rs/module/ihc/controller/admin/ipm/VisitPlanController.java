package com.rs.module.ihc.controller.admin.ipm;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.entity.ipm.VisitPlanDO;
import com.rs.module.ihc.service.ipm.VisitPlanService;

@Api(tags = "管理后台 - 所内就医-现场巡诊计划")
@RestController
@RequestMapping("/ihc/ipm/visit-plan")
@Validated
public class VisitPlanController {

    @Resource
    private VisitPlanService visitPlanService;

    @PostMapping("/create")
    @ApiOperation(value = "创建所内就医-现场巡诊计划")
    @LogRecordAnnotation(bizModule = "ihc:visitPlan:create", operateType = LogOperateType.CREATE, title = "创建所内就医-现场巡诊计划",
    success = "创建所内就医-现场巡诊计划成功", fail = "创建所内就医-现场巡诊计划失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createVisitPlan(@Valid @RequestBody VisitPlanSaveReqVO createReqVO) {
        return success(visitPlanService.createVisitPlan(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新所内就医-现场巡诊计划")
    @LogRecordAnnotation(bizModule = "ihc:visitPlan:update", operateType = LogOperateType.UPDATE, title = "更新所内就医-现场巡诊计划",
    success = "更新所内就医-现场巡诊计划成功", fail = "更新所内就医-现场巡诊计划失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateVisitPlan(@Valid @RequestBody VisitPlanSaveReqVO updateReqVO) {
        visitPlanService.updateVisitPlan(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除所内就医-现场巡诊计划")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:visitPlan:delete", operateType = LogOperateType.DELETE, title = "删除所内就医-现场巡诊计划",
    success = "删除所内就医-现场巡诊计划成功", fail = "删除所内就医-现场巡诊计划失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteVisitPlan(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           visitPlanService.deleteVisitPlan(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得所内就医-现场巡诊计划")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:visitPlan:get", operateType = LogOperateType.QUERY, title = "获取所内就医-现场巡诊计划", bizNo = "{{#id}}", success = "获取所内就医-现场巡诊计划成功", fail = "获取所内就医-现场巡诊计划失败", extraInfo = "{{#id}}")
    public CommonResult<VisitPlanRespVO> getVisitPlan(@RequestParam("id") String id) {
        VisitPlanDO visitPlan = visitPlanService.getVisitPlan(id);
        return success(BeanUtils.toBean(visitPlan, VisitPlanRespVO.class));
    }

    @GetMapping("/page")
    @ApiOperation(value = "获得所内就医-现场巡诊计划分页")
    @LogRecordAnnotation(bizModule = "ihc:visitPlan:page", operateType = LogOperateType.QUERY, title = "获得所内就医-现场巡诊计划分页",
    success = "获得所内就医-现场巡诊计划分页成功", fail = "获得所内就医-现场巡诊计划分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<VisitPlanRespVO>> getVisitPlanPage(@Valid VisitPlanPageReqVO pageReqVO) {
        PageResult<VisitPlanDO> pageResult = visitPlanService.getVisitPlanPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, VisitPlanRespVO.class));
    }

    @GetMapping("/list")
    @ApiOperation(value = "获得所内就医-现场巡诊计划列表")
    @LogRecordAnnotation(bizModule = "ihc:visitPlan:list", operateType = LogOperateType.QUERY, title = "获得所内就医-现场巡诊计划列表",
    success = "获得所内就医-现场巡诊计划列表成功", fail = "获得所内就医-现场巡诊计划列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<VisitPlanRespVO>> getVisitPlanList(@Valid VisitPlanListReqVO listReqVO) {
    List<VisitPlanDO> list = visitPlanService.getVisitPlanList(listReqVO);
        return success(BeanUtils.toBean(list, VisitPlanRespVO.class));
    }

}
