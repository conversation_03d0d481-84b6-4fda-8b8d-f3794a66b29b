package com.rs.module.ihc.controller.admin.ephe;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.ephe.vo.DisinfectRespVO;
import com.rs.module.ihc.controller.admin.ephe.vo.DisinfectSaveReqVO;
import com.rs.module.ihc.entity.ephe.DisinfectDO;
import com.rs.module.ihc.service.ephe.DisinfectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "卫生防疫与健康教育-卫生消毒登记")
@RestController
@RequestMapping("/ihc/ephe/disinfect")
@Validated
public class DisinfectController {

    @Resource
    private DisinfectService disinfectService;

    @PostMapping("/create")
    @ApiOperation(value = "创建卫生防疫与健康教育-卫生消毒登记")
    @LogRecordAnnotation(bizModule = "ihc:ephe:disinfect:create", operateType = LogOperateType.CREATE, title = "卫生防疫与健康教育-卫生消毒登记",
            success = "卫生防疫与健康教育-卫生消毒登记-成功", fail = "卫生防疫与健康教育-卫生消毒登记，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createDisinfect(@Valid @RequestBody DisinfectSaveReqVO createReqVO) {
        return success(disinfectService.createDisinfect(createReqVO));
    }


    @GetMapping("/get")
    @ApiOperation(value = "获得卫生防疫与健康教育-卫生消毒登记")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:ephe:disinfect-get", operateType = LogOperateType.QUERY, title = "获得卫生防疫与健康教育-卫生消毒登记-信息获取",
            success = "获得卫生防疫与健康教育-卫生消毒登记-信息获取-成功", fail = "卫生防疫与健康教育-疫情处置-信息获取-错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    public CommonResult<DisinfectRespVO> getDisinfect(@RequestParam("id") String id) {
        DisinfectDO disinfect = disinfectService.getDisinfect(id);
        return success(BeanUtils.toBean(disinfect, DisinfectRespVO.class));
    }

}
