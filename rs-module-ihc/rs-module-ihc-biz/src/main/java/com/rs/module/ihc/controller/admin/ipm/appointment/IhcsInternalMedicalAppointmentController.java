package com.rs.module.ihc.controller.admin.ipm.appointment;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.ipm.appointment.dto.AuditMedicalAppointmentDTO;
import com.rs.module.ihc.controller.admin.ipm.appointment.dto.BatchAuditNoRequireProcessDTO;
import com.rs.module.ihc.controller.admin.ipm.appointment.dto.SaveMedicalAppointmentDTO;
import com.rs.module.ihc.controller.admin.ipm.appointment.vo.*;
import com.rs.module.ihc.entity.ipm.appointment.IhcsInternalMedicalAppointmentDO;
import com.rs.module.ihc.service.ipm.appointment.IhcsInternalMedicalAppointmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "所内就医-预约登记")
@RestController
@RequestMapping("/ihc/internalMedicalAppointment")
@Validated
public class IhcsInternalMedicalAppointmentController {

    @Resource
    private IhcsInternalMedicalAppointmentService sInternalMedicalAppointmentService;


    @PostMapping("/create")
    @ApiOperation(value = "创建所内就医- 在线报病")
    public CommonResult<String> createsInternalMedicalAppointment(@Valid @RequestBody SaveMedicalAppointmentDTO createReqVO) {
        return success(sInternalMedicalAppointmentService.createsInternalMedicalAppointment(createReqVO));
    }

    @PostMapping("/audit")
    @ApiOperation("审核预约信息")
    public CommonResult<Void> auditMedicalAppointment(@RequestBody @Valid AuditMedicalAppointmentDTO auditMedicalAppointmentDTO) {
        sInternalMedicalAppointmentService.auditMedicalAppointment(auditMedicalAppointmentDTO);
        return CommonResult.success();
    }

    @PostMapping("/batchAuditNoRequireProcess")
    @ApiOperation("批量审核预约信息无需处理")
    public CommonResult<Void> batchAuditNoRequireProcess(@RequestBody @Valid BatchAuditNoRequireProcessDTO batchAuditNoRequireProcessDTO) {
        sInternalMedicalAppointmentService.batchAuditNoRequireProcess(batchAuditNoRequireProcessDTO);
        return CommonResult.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新所内就医-预约登记")
    public CommonResult<Boolean> updatesInternalMedicalAppointment(@Valid @RequestBody IhcsInternalMedicalAppointmentSaveReqVO updateReqVO) {
        sInternalMedicalAppointmentService.updatesInternalMedicalAppointment(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除所内就医-预约登记")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deletesInternalMedicalAppointment(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            sInternalMedicalAppointmentService.deletesInternalMedicalAppointment(id);
        }
        return success(true);

    }

//    @GetMapping("/get")
//    @ApiOperation(value = "获得所内就医-预约登记")
//    @ApiImplicitParam(name = "id", value = "编号")
//    public CommonResult<String> getsInternalMedicalAppointment(@RequestParam("id") String id) {
//        IhcsInternalMedicalAppointmentDO sInternalMedicalAppointment = sInternalMedicalAppointmentService.getsInternalMedicalAppointment(id);
//        return success("4");
//    }


    @PostMapping("/page")
    @ApiOperation(value = "获得所内就医-预约登记分页")
    public CommonResult<PageResult<IhcsInternalMedicalAppointmentRespVO>> getsInternalMedicalAppointmentPage(@Valid @RequestBody IhcsInternalMedicalAppointmentPageReqVO pageReqVO) {
        PageResult<IhcsInternalMedicalAppointmentDO> pageResult = sInternalMedicalAppointmentService.getsInternalMedicalAppointmentPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, IhcsInternalMedicalAppointmentRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得所内就医-预约登记列表")
    public CommonResult<List<IhcsInternalMedicalAppointmentRespVO>> getsInternalMedicalAppointmentList(@Valid @RequestBody IhcsInternalMedicalAppointmentListReqVO listReqVO) {
        List<IhcsInternalMedicalAppointmentDO> list = sInternalMedicalAppointmentService.getsInternalMedicalAppointmentList(listReqVO);
        return success(BeanUtils.toBean(list, IhcsInternalMedicalAppointmentRespVO.class));
    }

    @GetMapping("/getById")
    @ApiOperation("根据id获取预约信息详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "预约id", dataType = "String", paramType = "query", required = true)
    })
    public CommonResult<GetMedicalAppointmentByIdVO> getMedicalAppointmentById(@RequestParam @NotNull(message = "预约id不能为空") String id) {


        return CommonResult.success(sInternalMedicalAppointmentService.getMedicalAppointmentById(id));
    }



}
