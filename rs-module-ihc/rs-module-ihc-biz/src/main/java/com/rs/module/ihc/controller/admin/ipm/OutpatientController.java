package com.rs.module.ihc.controller.admin.ipm;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.entity.ipm.OutpatientDO;
import com.rs.module.ihc.entity.ipm.PrescribeDO;
import com.rs.module.ihc.service.ipm.OutpatientService;
import com.rs.module.ihc.service.ipm.PrescribeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 所内就医-所内门诊")
@RestController
@RequestMapping("/ihc/ipm/outpatient")
@Validated
public class OutpatientController {

    @Resource
    private OutpatientService outpatientService;

    @Resource
    private PrescribeService prescribeService;

    @PostMapping("/create")
    @ApiOperation(value = "创建所内就医-所内门诊")
    @LogRecordAnnotation(bizModule = "ihc:outpatient:create", operateType = LogOperateType.CREATE, title = "创建所内就医-所内门诊",
            success = "创建所内就医-所内门诊成功", fail = "创建所内就医-所内门诊失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createOutpatient(@Valid @RequestBody OutpatientOnlySaveReqVO createReqVO) {
        return success(outpatientService.createOutpatient(BeanUtils.toBean(createReqVO, OutpatientSaveReqVO.class)));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新所内就医-所内门诊")
    @LogRecordAnnotation(bizModule = "ihc:outpatient:update", operateType = LogOperateType.UPDATE, title = "更新所内就医-所内门诊",
            success = "更新所内就医-所内门诊成功", fail = "更新所内就医-所内门诊失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateOutpatient(@Valid @RequestBody OutpatientSaveReqVO updateReqVO) {
        outpatientService.updateOutpatient(updateReqVO);
        return success(true);
    }

    @PostMapping("/create-cf")
    @ApiOperation(value = "创建所内就医-所内门诊-处方")
    public CommonResult<Boolean> createOutpatientCf(@Valid @RequestBody OutpatientSaveReqVO createReqVO) {
        outpatientService.createOutpatientCf(createReqVO);
        return success(true);
    }

    @PostMapping("/update-cf")
    @ApiOperation(value = "更新所内就医-所内门诊-处方")
    public CommonResult<Boolean> updateOutpatientCf(@Valid @RequestBody OutpatientSaveReqVO createReqVO) {
        outpatientService.updateOutpatientCf(createReqVO);
        return success(true);
    }

    @GetMapping("/get-cf")
    @ApiOperation(value = "获得所内就医-所内门诊-处方")
    @ApiImplicitParam(name = "id", value = "处方id")
    public CommonResult<OutpatientCfRespVO> getCf(@RequestParam("id") String id) {
        PrescribeDO prescribe = prescribeService.getPrescribe(id);
        OutpatientDO outpatient = outpatientService.getOutpatient(prescribe.getBusinessId());
        OutpatientCfRespVO outpatientCfRespVO = null;
        if (outpatient != null) {
            outpatientCfRespVO = BeanUtils.toBean(outpatient, OutpatientCfRespVO.class);
        }else {
            outpatientCfRespVO = new OutpatientCfRespVO();
        }
        outpatientCfRespVO.setCfid(prescribe.getId());
        outpatientCfRespVO.setJgrybm(prescribe.getJgrybm());
        return success(outpatientCfRespVO);
    }

    @PostMapping("/create-jcd")
    @ApiOperation(value = "创建所内就医-所内门诊-检查单")
    public CommonResult<Boolean> createOutpatientJcd(@Valid @RequestBody OutpatientSaveReqVO createReqVO) {
        outpatientService.createOutpatientCf(createReqVO);
        return success(true);
    }




    @GetMapping("/delete")
    @ApiOperation(value = "删除所内就医-所内门诊")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:outpatient:delete", operateType = LogOperateType.DELETE, title = "删除所内就医-所内门诊",
            success = "删除所内就医-所内门诊成功", fail = "删除所内就医-所内门诊失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteOutpatient(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            outpatientService.deleteOutpatient(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得所内就医-所内门诊")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:outpatient:get", operateType = LogOperateType.QUERY, title = "获取所内就医-所内门诊", bizNo = "{{#id}}", success = "获取所内就医-所内门诊成功", fail = "获取所内就医-所内门诊失败", extraInfo = "{{#id}}")
    public CommonResult<OutpatientRespVO> getOutpatient(@RequestParam("id") String id) {
        OutpatientDO outpatient = outpatientService.getOutpatient(id);
        return success(BeanUtils.toBean(outpatient, OutpatientRespVO.class));
    }

    @GetMapping("/page")
    @ApiOperation(value = "获得所内就医-所内门诊分页")
    @LogRecordAnnotation(bizModule = "ihc:outpatient:page", operateType = LogOperateType.QUERY, title = "获得所内就医-所内门诊分页",
            success = "获得所内就医-所内门诊分页成功", fail = "获得所内就医-所内门诊分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<OutpatientRespVO>> getOutpatientPage(@Valid OutpatientPageReqVO pageReqVO) {
        PageResult<OutpatientDO> pageResult = outpatientService.getOutpatientPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, OutpatientRespVO.class));
    }

    @GetMapping("/list")
    @ApiOperation(value = "获得所内就医-所内门诊列表")
    @LogRecordAnnotation(bizModule = "ihc:outpatient:list", operateType = LogOperateType.QUERY, title = "获得所内就医-所内门诊列表",
            success = "获得所内就医-所内门诊列表成功", fail = "获得所内就医-所内门诊列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<OutpatientRespVO>> getOutpatientList(@Valid OutpatientListReqVO listReqVO) {
        List<OutpatientDO> list = outpatientService.getOutpatientList(listReqVO);
        return success(BeanUtils.toBean(list, OutpatientRespVO.class));
    }




}
