package com.rs.module.ihc.controller.admin.ephe;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.ephe.vo.RoomDisinfectRespVO;
import com.rs.module.ihc.controller.admin.ephe.vo.RoomDisinfectSaveReqVO;
import com.rs.module.ihc.entity.ephe.RoomDisinfectDO;
import com.rs.module.ihc.service.ephe.RoomDisinfectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "卫生防疫与健康教育--监室消毒")
@RestController
@RequestMapping("/ihc/ephe/roomDisinfect")
@Validated
public class RoomDisinfectController {

    @Resource
    private RoomDisinfectService roomDisinfectService;

    @PostMapping("/create")
    @ApiOperation(value = "创建卫生防疫与健康教育--监室消毒")
    public CommonResult<String> createRoomDisinfect(@Valid @RequestBody RoomDisinfectSaveReqVO createReqVO) {
        return success(roomDisinfectService.createRoomDisinfect(createReqVO));
    }
    @PostMapping("/update")
    @ApiOperation(value = "更新卫生防疫与健康教育--监室消毒")
    public CommonResult<Boolean> updateRoomDisinfect(@Valid @RequestBody RoomDisinfectSaveReqVO updateReqVO) {
        roomDisinfectService.updateRoomDisinfect(updateReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得卫生防疫与健康教育--监室消毒")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<RoomDisinfectRespVO> getRoomDisinfect(@RequestParam("id") String id) {
        RoomDisinfectDO roomDisinfect = roomDisinfectService.getRoomDisinfect(id);
        return success(BeanUtils.toBean(roomDisinfect, RoomDisinfectRespVO.class));
    }
}
