package com.rs.module.ihc.controller.admin.ipm;

import com.rs.module.ihc.events.event.ipm.PrescribeStateEvent;
import com.rs.module.ihc.util.EventPublisherUtil;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.entity.ipm.PrescribeDO;
import com.rs.module.ihc.entity.ipm.PrescribeMedicineDO;
import com.rs.module.ihc.service.ipm.PrescribeService;

@Api(tags = "管理后台 - 所内就医-处方")
@RestController
@RequestMapping("/ihc/ipm/prescribe")
@Validated
public class PrescribeController {

    @Resource
    private PrescribeService prescribeService;


    @PostMapping("/create")
    @ApiOperation(value = "创建所内就医-处方")
    @LogRecordAnnotation(bizModule = "ihc:prescribe:create", operateType = LogOperateType.CREATE, title = "创建所内就医-处方",
    success = "创建所内就医-处方成功", fail = "创建所内就医-处方失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createPrescribe(@Valid @RequestBody PrescribeSaveReqVO createReqVO) {
        return success(prescribeService.createPrescribe(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新所内就医-处方")
    @LogRecordAnnotation(bizModule = "ihc:prescribe:update", operateType = LogOperateType.UPDATE, title = "更新所内就医-处方",
    success = "更新所内就医-处方成功", fail = "更新所内就医-处方失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updatePrescribe(@Valid @RequestBody PrescribeSaveReqVO updateReqVO) {
        prescribeService.updatePrescribe(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除所内就医-处方")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:prescribe:delete", operateType = LogOperateType.DELETE, title = "删除所内就医-处方",
    success = "删除所内就医-处方成功", fail = "删除所内就医-处方失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deletePrescribe(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           prescribeService.deletePrescribe(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得所内就医-处方")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:prescribe:get", operateType = LogOperateType.QUERY, title = "获取所内就医-处方", bizNo = "{{#id}}", success = "获取所内就医-处方成功", fail = "获取所内就医-处方失败", extraInfo = "{{#id}}")
    public CommonResult<PrescribeRespVO> getPrescribe(@RequestParam("id") String id) {
        PrescribeDO prescribe = prescribeService.getPrescribe(id);
        EventPublisherUtil.publishEvent(new PrescribeStateEvent(prescribe));
        return success(BeanUtils.toBean(prescribe, PrescribeRespVO.class));
    }

    @GetMapping("/page")
    @ApiOperation(value = "获得所内就医-处方分页")
    @LogRecordAnnotation(bizModule = "ihc:prescribe:page", operateType = LogOperateType.QUERY, title = "获得所内就医-处方分页",
    success = "获得所内就医-处方分页成功", fail = "获得所内就医-处方分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<PrescribeRespVO>> getPrescribePage(@Valid PrescribePageReqVO pageReqVO) {
        PageResult<PrescribeDO> pageResult = prescribeService.getPrescribePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PrescribeRespVO.class));
    }

    @GetMapping("/list")
    @ApiOperation(value = "获得所内就医-处方列表")
    @LogRecordAnnotation(bizModule = "ihc:prescribe:list", operateType = LogOperateType.QUERY, title = "获得所内就医-处方列表",
    success = "获得所内就医-处方列表成功", fail = "获得所内就医-处方列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<PrescribeRespVO>> getPrescribeList(@Valid PrescribeListReqVO listReqVO) {
    List<PrescribeDO> list = prescribeService.getPrescribeList(listReqVO);
        return success(BeanUtils.toBean(list, PrescribeRespVO.class));
    }

    // ==================== 子表（所内就医-处方-药方信息） ====================
    @GetMapping("/prescribe-medicine/list-by-mlh")
    @ApiOperation(value = "获得所内就医-处方-药方信息列表")
    @ApiImplicitParam(name = "mlh", value = "处方id，对应ihc_ipm_prescribe.id")
    public CommonResult<List<PrescribeMedicineRespVO>> getPrescribeMedicineListByMlh(@RequestParam("mlh") String mlh) {
        List<PrescribeMedicineRespVO> prescribeMedicineListByMlh = new ArrayList<>();
        for (String s : mlh.split(",")) {
            List<PrescribeMedicineDO> prescribeMedicineDOList = prescribeService.getPrescribeMedicineListByMlh(s);
            prescribeMedicineListByMlh.addAll(BeanUtils.toBean(prescribeMedicineDOList, PrescribeMedicineRespVO.class));
        }
        return success(prescribeMedicineListByMlh);
    }
    @ApiOperation(value = "处方状态变更")
    @PostMapping("/status/change")
    public CommonResult<Boolean> prescribeStatusChange(@RequestBody @Valid PrescribeStatusChangeReqVO prescribeStatusChangeDTO) {
        prescribeService.prescribeStatusChange(prescribeStatusChangeDTO);
        return CommonResult.success(true);
    }



}
