package com.rs.module.ihc.controller.admin.ipm;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.entity.ipm.VisitDO;
import com.rs.module.ihc.entity.ipm.VisitMedicineDO;
import com.rs.module.ihc.service.ipm.VisitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 所内就医-现场巡检")
@RestController
@RequestMapping("/ihc/ipm/visit")
@Validated
public class VisitController {

    @Resource
    private VisitService visitService;

    @PostMapping("/create")
    @ApiOperation(value = "创建所内就医-现场巡检-现场司药")
    public CommonResult<String> createVisit(@Valid @RequestBody VisitSaveReqVO createReqVO) {
        createReqVO.setVisitProcessMethod("1");
        return success(visitService.createVisit(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新所内就医-现场巡检-现场司药")
    @LogRecordAnnotation(bizModule = "ihc:visit:update", operateType = LogOperateType.UPDATE, title = "更新所内就医-现场巡检",
            success = "更新所内就医-现场巡检成功", fail = "更新所内就医-现场巡检失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateVisit(@Valid @RequestBody VisitSaveReqVO updateReqVO) {
        visitService.updateVisit(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除所内就医-现场巡检-现场司药")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:visit:delete", operateType = LogOperateType.DELETE, title = "删除所内就医-现场巡检",
            success = "删除所内就医-现场巡检成功", fail = "删除所内就医-现场巡检失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteVisit(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            visitService.deleteVisit(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得所内就医-现场巡检-现场司药")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:visit:get", operateType = LogOperateType.QUERY, title = "获取所内就医-现场巡检", bizNo = "{{#id}}", success = "获取所内就医-现场巡检成功", fail = "获取所内就医-现场巡检失败", extraInfo = "{{#id}}")
    public CommonResult<VisitRespVO> getVisit(@RequestParam("id") String id) {
        VisitDO visit = visitService.getVisit(id);
        return success(BeanUtils.toBean(visit, VisitRespVO.class));
    }

    @GetMapping("/page")
    @ApiOperation(value = "获得所内就医-现场巡检分页")
    @LogRecordAnnotation(bizModule = "ihc:visit:page", operateType = LogOperateType.QUERY, title = "获得所内就医-现场巡检分页",
            success = "获得所内就医-现场巡检分页成功", fail = "获得所内就医-现场巡检分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<VisitRespVO>> getVisitPage(@Valid VisitPageReqVO pageReqVO) {
        PageResult<VisitDO> pageResult = visitService.getVisitPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, VisitRespVO.class));
    }

    @GetMapping("/list")
    @ApiOperation(value = "获得所内就医-现场巡检列表")
    @LogRecordAnnotation(bizModule = "ihc:visit:list", operateType = LogOperateType.QUERY, title = "获得所内就医-现场巡检列表",
            success = "获得所内就医-现场巡检列表成功", fail = "获得所内就医-现场巡检列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<VisitRespVO>> getVisitList(@Valid VisitListReqVO listReqVO) {
        List<VisitDO> list = visitService.getVisitList(listReqVO);
        return success(BeanUtils.toBean(list, VisitRespVO.class));
    }

    // ==================== 子表（所内就医-现场巡诊-现场司药信息） ====================

    @GetMapping("/visit-medicine/list-by-visit-id")
    @ApiOperation(value = "获得所内就医-现场巡诊-现场司药信息列表")
    @ApiImplicitParam(name = "visitId", value = "现场巡诊id，对应ihc_ipm_visit.id")
    public CommonResult<List<VisitMedicineDO>> getVisitMedicineListByVisitId(@RequestParam("visitId") String visitId) {
        return success(visitService.getVisitMedicineListByVisitId(visitId));
    }

    @PostMapping("/create-xcxz-cf")
    @ApiOperation(value = "创建所内就医-现场巡检-处方")
    public CommonResult<String> createVisitCf(@Valid @RequestBody VisitSaveCfReqVO visitSaveCfReqVO) {
        visitSaveCfReqVO.setVisitProcessMethod("2");
        return success(visitService.createVisitCf(visitSaveCfReqVO));
    }

    @PostMapping("/update-xcxz-cf")
    @ApiOperation(value = "更新所内就医-现场巡检-处方")
    public CommonResult<Boolean> updateVisitCf(@Valid @RequestBody VisitSaveCfReqVO visitSaveCfReqVO) {
        visitService.updateVisitCf(visitSaveCfReqVO);
        return success(true);
    }

    @PostMapping("/create-xcxz-wxyy")
    @ApiOperation(value = "创建所内就医-现场巡检-无需用药")
    public CommonResult<String> createVisitWxcl(@Valid @RequestBody VisitSaveWxclReqVO visitSaveCfReqVO) {
        visitSaveCfReqVO.setVisitProcessMethod("3");
        return success(visitService.createVisitWxyy(visitSaveCfReqVO));
    }


}
