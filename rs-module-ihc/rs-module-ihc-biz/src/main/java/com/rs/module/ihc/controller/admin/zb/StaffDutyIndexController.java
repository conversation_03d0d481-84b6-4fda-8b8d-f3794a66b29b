package com.rs.module.ihc.controller.admin.zb;

import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.base.controller.admin.zh.vo.staffduty.DutyManageCopyDTO;
import com.rs.module.base.controller.admin.zh.vo.staffduty.DutyManageHeaderVO;
import com.rs.module.base.controller.admin.zh.vo.staffduty.DutyManageSearchDTO;
import com.rs.module.base.controller.admin.zh.vo.staffduty.DutyManageVO;
import com.rs.module.base.enums.StaffDutyTypeEnum;
import com.rs.module.base.service.zh.staffduty.StaffDutyIndexService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;

@Api(value = "值班登记", tags = "值班登记")
@Slf4j
@RequestMapping("/ihc/zb/staffDutyIndex")
@RestController
@Validated
public class StaffDutyIndexController {

    @Autowired
    private StaffDutyIndexService indexService;

    @ApiOperation(value = "值班排版首页列表")
    @GetMapping(value = "/indexListByDutyDate")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "startTime", value = "开始时间", dataType = "date"),
            @ApiImplicitParam(paramType = "query", name = "endTime", value = "结束时间", dataType = "date")
    })
    public CommonResult<List<DutyManageVO>> indexListByDutyDate(@RequestParam Date startTime, @RequestParam Date endTime) {
        return CommonResult.success(indexService.indexListByDutyDate(startTime, endTime, StaffDutyTypeEnum.DOCTOR.getCode()));
    }

    @ApiOperation(value = "管教岗-值班排版首页列表")
    @GetMapping(value = "/getIndexHeaderListByNowDay")
    @ApiImplicitParam(paramType = "query", name = "queryType", value = "查询类型 1查询所有，不传则根据岗位区分", dataType = "Integer")
    public CommonResult<List<DutyManageHeaderVO>> getIndexHeaderListByNowDay(Integer queryType) {
        return CommonResult.success(indexService.getIndexHeaderListByNowDay(queryType,  StaffDutyTypeEnum.DOCTOR.getCode()));
    }

    @ApiOperation(value = "值班导出")
    @PostMapping(value = "/exportByDutyDate")
    public void exportByDutyDate(@RequestBody DutyManageSearchDTO manageSearchDTO, HttpServletResponse response) throws IOException {
        indexService.exportByDutyDate(manageSearchDTO.getStartTime(), manageSearchDTO.getEndTime(), StaffDutyTypeEnum.DOCTOR.getCode(), response);
    }

    @ApiOperation(value = "复制排班-日期过滤")
    @GetMapping(value = "/checkCopyDate")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "startTime", value = "开始时间", dataType = "date"),
            @ApiImplicitParam(paramType = "query", name = "endTime", value = "结束时间", dataType = "date")
    })
    public CommonResult<List<String>> checkCopyDate(@RequestParam Date startTime, @RequestParam Date endTime) {
        return CommonResult.success(indexService.checkCopyDate(startTime, endTime, StaffDutyTypeEnum.DOCTOR.getCode()));
    }

    @ApiOperation(value = "复制排班-日期校验(下一步校验)")
    @GetMapping(value = "/checkCopyDateNext")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "startTime", value = "开始时间", dataType = "date"),
            @ApiImplicitParam(paramType = "query", name = "endTime", value = "结束时间", dataType = "date")
    })
    public CommonResult<String> checkCopyDateNext(@RequestParam Date startTime, @RequestParam Date endTime) {
        Integer result = indexService.checkCopyDateNext(startTime, endTime, StaffDutyTypeEnum.DOCTOR.getCode());
        if (result == 1) {
            return CommonResult.success("校验成功");
        } else if (result == -1) {
            return CommonResult.error("您选择的排班记录不符合要求，请重新选择！");
        } else if (result == -2) {
            return CommonResult.error("您选择的排班记录不符合要求，请重新选择！");
        } else {
            return CommonResult.error("系统异常，请稍后再试，或者与管理员反馈");
        }

    }

    @ApiOperation(value = "复制排班-提交是否存在覆盖校验)")
    @GetMapping(value = "/checkHasData")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "startTime", value = "开始时间", dataType = "date"),
            @ApiImplicitParam(paramType = "query", name = "endTime", value = "结束时间", dataType = "date")
    })
    public CommonResult<Boolean> checkHasData(@RequestParam Date startTime, @RequestParam Date endTime) {
        Boolean result = indexService.checkHasData(startTime, endTime, StaffDutyTypeEnum.DOCTOR.getCode());
        return CommonResult.success(result);

    }

    @ApiOperation(value = "复制排班-确认提交排班")
    @PostMapping(value = "/copyData")
    public CommonResult<String> copyData(@RequestBody DutyManageCopyDTO dutyManageCopyDTO) {
        dutyManageCopyDTO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        Integer result = indexService.copyData(dutyManageCopyDTO, StaffDutyTypeEnum.DOCTOR.getCode());
        if (result == 1) {
            return CommonResult.success("复制成功");
        } else if (result == -1) {
            return CommonResult.error("源排班和目标排班日期长度不一致！");
        } else if (result == -2) {
            return CommonResult.error("目标排班时间必须在今天以后！");
        } else {
            return CommonResult.error("系统异常，请稍后再试，或者与管理员反馈");
        }
    }

}
