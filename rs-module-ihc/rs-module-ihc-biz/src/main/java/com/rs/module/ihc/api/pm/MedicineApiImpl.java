package com.rs.module.ihc.api.pm;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.api.pm.dto.MedicineRespDTO;
import com.rs.module.ihc.entity.pm.MedicineDO;
import com.rs.module.ihc.service.pm.MedicineService;

/**
 * RPC服务-药品实现类，提供RESTful API接口，给Feign调用
 * <AUTHOR>
 * @date 2025年3月20日
 */
@RestController
@Validated
public class MedicineApiImpl implements MedicineApi{
	
	@Resource
	MedicineService medicineService;

	@Override
	public CommonResult<MedicineRespDTO> getMedicine(String id){
		MedicineDO medicine = medicineService.getMedicine(id);
		return CommonResult.success(BeanUtils.toBean(medicine, MedicineRespDTO.class));
	}
}
