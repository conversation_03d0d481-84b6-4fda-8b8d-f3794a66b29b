package com.rs.module.ihc.controller.admin.pm.sick;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.pm.sick.vo.*;
import com.rs.module.ihc.entity.pm.sick.SeverelySickManageDO;
import com.rs.module.ihc.enums.SpecialPatientStatusEnum;
import com.rs.module.ihc.service.pm.sick.SeverelySickManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "重特病号管理")
@RestController
@RequestMapping("/ihc/pm/sick/manage")
@Validated
public class SeverelySickManageController {

    @Resource
    private SeverelySickManageService severelySickManageService;

    @PostMapping("/create")
    @ApiOperation(value = "创建重特病号管理")
    public CommonResult<String> createSeverelySickManage(@Valid @RequestBody SeverelySickManageSaveReqVO createReqVO) {
        return success(severelySickManageService.createSeverelySickManage(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新重特病号管理")
    public CommonResult<Boolean> updateSeverelySickManage(@Valid @RequestBody SeverelySickManageSaveReqVO updateReqVO) {
        severelySickManageService.updateSeverelySickManage(updateReqVO);
        return success(true);
    }

    @PostMapping("/release")
    @ApiOperation(value = "解除重特病号管理")
    public CommonResult<Boolean> release(@Valid @RequestBody SeverelySickManageReleaseReqVO updateReqVO) {
        updateReqVO.setRelieveTime(new Date());
        updateReqVO.setBusinessStatus(SpecialPatientStatusEnum.UNLISTED.getCode());
        severelySickManageService.updateSeverelySickManage(BeanUtils.toBean(updateReqVO, SeverelySickManageSaveReqVO.class));
        return success(true);
    }


    @GetMapping("/delete")
    @ApiOperation(value = "删除重特病号管理")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteSeverelySickManage(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            severelySickManageService.deleteSeverelySickManage(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得重特病号管理")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<SeverelySickManageRespVO> getSeverelySickManage(@RequestParam("id") String id) {
        SeverelySickManageDO severelySickManage = severelySickManageService.getSeverelySickManage(id);
        return success(BeanUtils.toBean(severelySickManage, SeverelySickManageRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得重特病号管理分页")
    public CommonResult<PageResult<SeverelySickManageRespVO>> getSeverelySickManagePage(@Valid @RequestBody SeverelySickManagePageReqVO pageReqVO) {
        PageResult<SeverelySickManageDO> pageResult = severelySickManageService.getSeverelySickManagePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SeverelySickManageRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得重特病号管理列表")
    public CommonResult<List<SeverelySickManageRespVO>> getSeverelySickManageList(@Valid @RequestBody SeverelySickManageListReqVO listReqVO) {
        List<SeverelySickManageDO> list = severelySickManageService.getSeverelySickManageList(listReqVO);
        return success(BeanUtils.toBean(list, SeverelySickManageRespVO.class));
    }
}
