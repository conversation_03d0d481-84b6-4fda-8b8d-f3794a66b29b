package com.rs.module.ihc.controller.admin.ipm;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.entity.ipm.VisitMedicineDO;
import com.rs.module.ihc.service.ipm.VisitMedicineService;

@Api(tags = "管理后台 - 所内就医-现场巡诊-现场司药信息")
@RestController
@RequestMapping("/ihc/ipm/visit-medicine")
@Validated
public class VisitMedicineController {

    @Resource
    private VisitMedicineService visitMedicineService;

    @PostMapping("/create")
    @ApiOperation(value = "创建所内就医-现场巡诊-现场司药信息")
    @LogRecordAnnotation(bizModule = "ihc:visitMedicine:create", operateType = LogOperateType.CREATE, title = "创建所内就医-现场巡诊-现场司药信息",
    success = "创建所内就医-现场巡诊-现场司药信息成功", fail = "创建所内就医-现场巡诊-现场司药信息失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createVisitMedicine(@Valid @RequestBody VisitMedicineSaveReqVO createReqVO) {
        return success(visitMedicineService.createVisitMedicine(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新所内就医-现场巡诊-现场司药信息")
    @LogRecordAnnotation(bizModule = "ihc:visitMedicine:update", operateType = LogOperateType.UPDATE, title = "更新所内就医-现场巡诊-现场司药信息",
    success = "更新所内就医-现场巡诊-现场司药信息成功", fail = "更新所内就医-现场巡诊-现场司药信息失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateVisitMedicine(@Valid @RequestBody VisitMedicineSaveReqVO updateReqVO) {
        visitMedicineService.updateVisitMedicine(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除所内就医-现场巡诊-现场司药信息")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:visitMedicine:delete", operateType = LogOperateType.DELETE, title = "删除所内就医-现场巡诊-现场司药信息",
    success = "删除所内就医-现场巡诊-现场司药信息成功", fail = "删除所内就医-现场巡诊-现场司药信息失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteVisitMedicine(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           visitMedicineService.deleteVisitMedicine(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得所内就医-现场巡诊-现场司药信息")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:visitMedicine:get", operateType = LogOperateType.QUERY, title = "获取所内就医-现场巡诊-现场司药信息", bizNo = "{{#id}}", success = "获取所内就医-现场巡诊-现场司药信息成功", fail = "获取所内就医-现场巡诊-现场司药信息失败", extraInfo = "{{#id}}")
    public CommonResult<VisitMedicineRespVO> getVisitMedicine(@RequestParam("id") String id) {
        VisitMedicineDO visitMedicine = visitMedicineService.getVisitMedicine(id);
        return success(BeanUtils.toBean(visitMedicine, VisitMedicineRespVO.class));
    }

}
