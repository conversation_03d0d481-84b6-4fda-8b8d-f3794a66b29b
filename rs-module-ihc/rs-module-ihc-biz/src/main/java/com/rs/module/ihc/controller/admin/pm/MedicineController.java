package com.rs.module.ihc.controller.admin.pm;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineListReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.MedicinePageReqVO;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineRespVO;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineSaveReqVO;
import com.rs.module.ihc.entity.pm.MedicineDO;
import com.rs.module.ihc.service.pm.MedicineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 药房管理-药品信息")
@RestController
@RequestMapping("/ihc/medicine")
@Validated
public class MedicineController {

    @Resource
    private MedicineService medicineService;

    @PostMapping("/create")
    @ApiOperation(value = "创建药房管理-药品信息")
    @LogRecordAnnotation(bizModule = "ihc:medicine:create", operateType = LogOperateType.CREATE, title = "创建药房管理-药品信息",
            success = "创建药房管理-药品信息成功", fail = "创建药房管理-药品信息失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createMedicine(@Valid @RequestBody MedicineSaveReqVO createReqVO) {
        return success(medicineService.createMedicine(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新药房管理-药品信息")
    @LogRecordAnnotation(bizModule = "ihc:medicine:update", operateType = LogOperateType.UPDATE, title = "更新药房管理-药品信息",
            success = "更新药房管理-药品信息成功", fail = "更新药房管理-药品信息失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateMedicine(@Valid @RequestBody MedicineSaveReqVO updateReqVO) {
        medicineService.updateMedicine(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除药房管理-药品信息")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:medicine:delete", operateType = LogOperateType.DELETE, title = "删除药房管理-药品信息",
            success = "删除药房管理-药品信息成功", fail = "删除药房管理-药品信息失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteMedicine(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            medicineService.deleteMedicine(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得药房管理-药品信息")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:medicine:get", operateType = LogOperateType.QUERY, title = "获取药房管理-药品信息", bizNo = "{{#id}}", success = "获取药房管理-药品信息成功", fail = "获取药房管理-药品信息失败", extraInfo = "{{#id}}")
    public CommonResult<MedicineRespVO> getMedicine(@RequestParam("id") String id) {
        MedicineDO medicine = medicineService.getMedicine(id);
        return success(BeanUtils.toBean(medicine, MedicineRespVO.class));
    }

    @GetMapping("/page")
    @ApiOperation(value = "获得药房管理-药品信息分页")
    @LogRecordAnnotation(bizModule = "ihc:medicine:page", operateType = LogOperateType.QUERY, title = "获得药房管理-药品信息分页",
            success = "获得药房管理-药品信息分页成功", fail = "获得药房管理-药品信息分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<MedicineRespVO>> getMedicinePage(@Valid MedicinePageReqVO pageReqVO) {

        PageResult<MedicineDO> pageResult = medicineService.getMedicinePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MedicineRespVO.class));
    }

    @GetMapping("/list")
    @ApiOperation(value = "获得药房管理-药品信息列表")
    @LogRecordAnnotation(bizModule = "ihc:medicine:list", operateType = LogOperateType.QUERY, title = "获得药房管理-药品信息列表",
            success = "获得药房管理-药品信息列表成功", fail = "获得药房管理-药品信息列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<MedicineRespVO>> getMedicineList(@Valid MedicineListReqVO listReqVO) {
        List<MedicineDO> list = medicineService.getMedicineList(listReqVO);
        return success(BeanUtils.toBean(list, MedicineRespVO.class));
    }

    @GetMapping("/init")
    public CommonResult<Map<String, String>> init() {
        medicineService.init();
        return success();
    }

}
