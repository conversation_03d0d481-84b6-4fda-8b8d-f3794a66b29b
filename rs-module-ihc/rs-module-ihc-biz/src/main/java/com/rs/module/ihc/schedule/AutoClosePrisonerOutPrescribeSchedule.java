package com.rs.module.ihc.schedule;

import com.rs.module.ihc.service.ipm.PrescribeService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@AllArgsConstructor
public class AutoClosePrisonerOutPrescribeSchedule implements ApplicationRunner {

    private final PrescribeService prescribeService;

    @Scheduled(cron = "0 0 0 * * ?")
    public void execute() {
        log.info("执行自动结束出所人员处方任务开始");
        prescribeService.autoClosePrisonerOutPrescribe();
        log.info("执行自动结束出所人员处方任务完成");
    }

    /**
     * 项目启动时执行一次
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        execute();
    }
}
