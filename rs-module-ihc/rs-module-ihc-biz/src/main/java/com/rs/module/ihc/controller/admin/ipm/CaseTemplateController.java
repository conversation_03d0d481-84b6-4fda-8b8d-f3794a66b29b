package com.rs.module.ihc.controller.admin.ipm;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.entity.ipm.CaseTemplateDO;
import com.rs.module.ihc.service.ipm.CaseTemplateService;

@Api(tags = "管理后台 - 所内就医-病例模板")
@RestController
@RequestMapping("/ihc/ipm/case-template")
@Validated
public class CaseTemplateController {

    @Resource
    private CaseTemplateService caseTemplateService;

    @PostMapping("/create")
    @ApiOperation(value = "创建所内就医-病例模板")
    @LogRecordAnnotation(bizModule = "ihc:caseTemplate:create", operateType = LogOperateType.CREATE, title = "创建所内就医-病例模板",
    success = "创建所内就医-病例模板成功", fail = "创建所内就医-病例模板失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createCaseTemplate(@Valid @RequestBody CaseTemplateSaveReqVO createReqVO) {
        return success(caseTemplateService.createCaseTemplate(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新所内就医-病例模板")
    @LogRecordAnnotation(bizModule = "ihc:caseTemplate:update", operateType = LogOperateType.UPDATE, title = "更新所内就医-病例模板",
    success = "更新所内就医-病例模板成功", fail = "更新所内就医-病例模板失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateCaseTemplate(@Valid @RequestBody CaseTemplateSaveReqVO updateReqVO) {
        caseTemplateService.updateCaseTemplate(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除所内就医-病例模板")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:caseTemplate:delete", operateType = LogOperateType.DELETE, title = "删除所内就医-病例模板",
    success = "删除所内就医-病例模板成功", fail = "删除所内就医-病例模板失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteCaseTemplate(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           caseTemplateService.deleteCaseTemplate(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得所内就医-病例模板")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:caseTemplate:get", operateType = LogOperateType.QUERY, title = "获取所内就医-病例模板", bizNo = "{{#id}}", success = "获取所内就医-病例模板成功", fail = "获取所内就医-病例模板失败", extraInfo = "{{#id}}")
    public CommonResult<CaseTemplateRespVO> getCaseTemplate(@RequestParam("id") String id) {
        CaseTemplateDO caseTemplate = caseTemplateService.getCaseTemplate(id);
        return success(BeanUtils.toBean(caseTemplate, CaseTemplateRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得所内就医-病例模板分页")
    @LogRecordAnnotation(bizModule = "ihc:caseTemplate:page", operateType = LogOperateType.QUERY, title = "获得所内就医-病例模板分页",
    success = "获得所内就医-病例模板分页成功", fail = "获得所内就医-病例模板分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<CaseTemplateRespVO>> getCaseTemplatePage(@Valid @RequestBody CaseTemplatePageReqVO pageReqVO) {
        PageResult<CaseTemplateDO> pageResult = caseTemplateService.getCaseTemplatePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CaseTemplateRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得所内就医-病例模板列表")
    @LogRecordAnnotation(bizModule = "ihc:caseTemplate:list", operateType = LogOperateType.QUERY, title = "获得所内就医-病例模板列表",
    success = "获得所内就医-病例模板列表成功", fail = "获得所内就医-病例模板列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<CaseTemplateRespVO>> getCaseTemplateList(@Valid @RequestBody CaseTemplateListReqVO listReqVO) {
    List<CaseTemplateDO> list = caseTemplateService.getCaseTemplateList(listReqVO);
        return success(BeanUtils.toBean(list, CaseTemplateRespVO.class));
    }

}
