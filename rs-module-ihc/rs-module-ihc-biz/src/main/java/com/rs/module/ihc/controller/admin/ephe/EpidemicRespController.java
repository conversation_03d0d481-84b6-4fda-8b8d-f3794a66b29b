package com.rs.module.ihc.controller.admin.ephe;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.ephe.vo.EpidemicRespRespVO;
import com.rs.module.ihc.controller.admin.ephe.vo.EpidemicRespSaveReqVO;
import com.rs.module.ihc.entity.ephe.EpidemicRespDO;
import com.rs.module.ihc.service.ephe.EpidemicRespService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "卫生防疫与健康教育-疫情处置")
@RestController
@RequestMapping("/ihc/ephe/epidemicResp")
@Validated
public class EpidemicRespController {

    @Resource
    private EpidemicRespService epidemicRespService;

    @PostMapping("/create")
    @ApiOperation(value = "创建卫生防疫与健康教育-疫情处置")
    @LogRecordAnnotation(bizModule = "ihc:ephe:epidemicResp:create", operateType = LogOperateType.CREATE, title = "卫生防疫与健康教育-疫情处置登记",
            success = "卫生防疫与健康教育-疫情处置育登记-成功", fail = "卫生防疫与健康教育-疫情处置，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createEpidemicResp(@Valid @RequestBody EpidemicRespSaveReqVO createReqVO) {
        return success(epidemicRespService.createEpidemicResp(createReqVO));
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得卫生防疫与健康教育-疫情处置")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:ephe:epidemicResp-get", operateType = LogOperateType.QUERY, title = "卫卫生防疫与健康教育-疫情处置-信息获取",
            success = "卫生防疫与健康教育-疫情处置-信息获取-成功", fail = "卫生防疫与健康教育-疫情处置-信息获取-错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    public CommonResult<EpidemicRespRespVO> getEpidemicResp(@RequestParam("id") String id) {
        EpidemicRespDO epidemicResp = epidemicRespService.getEpidemicResp(id);
        return success(BeanUtils.toBean(epidemicResp, EpidemicRespRespVO.class));
    }

}
