package com.rs.module.ihc.controller.admin.injury;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.injury.vo.InjuryRegistrationAttachListReqVO;
import com.rs.module.ihc.controller.admin.injury.vo.InjuryRegistrationAttachRespVO;
import com.rs.module.ihc.controller.admin.injury.vo.InjuryRegistrationRespVO;
import com.rs.module.ihc.controller.admin.injury.vo.InjuryRegistrationSaveReqVO;
import com.rs.module.ihc.entity.injury.InjuryRegistrationAttachDO;
import com.rs.module.ihc.entity.injury.InjuryRegistrationDO;
import com.rs.module.ihc.service.injury.InjuryRegistrationAttachService;
import com.rs.module.ihc.service.injury.InjuryRegistrationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "医疗子系统-伤情登记")
@RestController
@RequestMapping("/ihc/injury/injuryRegistration")
@Validated
public class InjuryRegistrationController {

    @Resource
    private InjuryRegistrationService injuryRegistrationService;

    @Resource
    private InjuryRegistrationAttachService attachService;

    @PostMapping("/create")
    @ApiOperation(value = "创建医疗子系统-伤情登记")
    @LogRecordAnnotation(bizModule = "ihc:injury:create", operateType = LogOperateType.CREATE, title = "医疗管理-伤情登记",
            success = "医疗管理-伤情登记-成功", fail = "医疗管理-伤情登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createInjuryRegistration(@Valid @RequestBody InjuryRegistrationSaveReqVO createReqVO) {
        return success(injuryRegistrationService.createInjuryRegistration(createReqVO));
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得医疗子系统-伤情登记")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:injury:create", operateType = LogOperateType.QUERY, title = "医疗管理-伤情登记",
            success = "医疗管理-伤情登记-成功", fail = "医疗管理-伤情登记-错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    public CommonResult<InjuryRegistrationRespVO> getInjuryRegistration(@RequestParam("id") String id) {
        InjuryRegistrationDO injuryRegistration = injuryRegistrationService.getInjuryRegistration(id);
        InjuryRegistrationRespVO injuryRegistrationRespVO = BeanUtils.toBean(injuryRegistration, InjuryRegistrationRespVO.class);
        if(injuryRegistrationRespVO != null){
            InjuryRegistrationAttachListReqVO injuryRegistrationAttachListReqVO = new InjuryRegistrationAttachListReqVO();
            injuryRegistrationAttachListReqVO.setRegistrationId(id);
            List<InjuryRegistrationAttachDO> attachList = attachService.getInjuryRegistrationAttachList(injuryRegistrationAttachListReqVO);
            injuryRegistrationRespVO.setAttachRespList(BeanUtils.toBean(attachList, InjuryRegistrationAttachRespVO.class));
        }
        return success(BeanUtils.toBean(injuryRegistration, InjuryRegistrationRespVO.class));
    }
}
