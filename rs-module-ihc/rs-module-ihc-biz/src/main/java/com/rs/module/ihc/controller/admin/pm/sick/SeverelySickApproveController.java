package com.rs.module.ihc.controller.admin.pm.sick;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.pm.sick.vo.SeverelySickApproveListReqVO;
import com.rs.module.ihc.controller.admin.pm.sick.vo.SeverelySickApprovePageReqVO;
import com.rs.module.ihc.controller.admin.pm.sick.vo.SeverelySickApproveRespVO;
import com.rs.module.ihc.controller.admin.pm.sick.vo.SeverelySickApproveSaveReqVO;
import com.rs.module.ihc.entity.pm.sick.SeverelySickApproveDO;
import com.rs.module.ihc.service.pm.sick.SeverelySickApproveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "重特病号审批")
@RestController
@RequestMapping("/ihc/pm.sick/severelySickApprove")
@Validated
public class SeverelySickApproveController {

    @Resource
    private SeverelySickApproveService severelySickApproveService;

    @PostMapping("/create")
    @ApiOperation(value = "创建重特病号审批")
    public CommonResult<String> createSeverelySickApprove(@Valid @RequestBody SeverelySickApproveSaveReqVO createReqVO) {
        return success(severelySickApproveService.createSeverelySickApprove(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新重特病号审批")
    public CommonResult<Boolean> updateSeverelySickApprove(@Valid @RequestBody SeverelySickApproveSaveReqVO updateReqVO) {
        severelySickApproveService.updateSeverelySickApprove(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除重特病号审批")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteSeverelySickApprove(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           severelySickApproveService.deleteSeverelySickApprove(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得重特病号审批")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<SeverelySickApproveRespVO> getSeverelySickApprove(@RequestParam("id") String id) {
        SeverelySickApproveDO severelySickApprove = severelySickApproveService.getSeverelySickApprove(id);
        return success(BeanUtils.toBean(severelySickApprove, SeverelySickApproveRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得重特病号审批分页")
    public CommonResult<PageResult<SeverelySickApproveRespVO>> getSeverelySickApprovePage(@Valid @RequestBody SeverelySickApprovePageReqVO pageReqVO) {
        PageResult<SeverelySickApproveDO> pageResult = severelySickApproveService.getSeverelySickApprovePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SeverelySickApproveRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得重特病号审批列表")
    public CommonResult<List<SeverelySickApproveRespVO>> getSeverelySickApproveList(@Valid @RequestBody SeverelySickApproveListReqVO listReqVO) {
        List<SeverelySickApproveDO> list = severelySickApproveService.getSeverelySickApproveList(listReqVO);
        return success(BeanUtils.toBean(list, SeverelySickApproveRespVO.class));
    }
}
