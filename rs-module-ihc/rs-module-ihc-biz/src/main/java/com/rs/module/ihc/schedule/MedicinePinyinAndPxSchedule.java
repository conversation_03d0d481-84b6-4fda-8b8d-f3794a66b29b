package com.rs.module.ihc.schedule;

import com.rs.module.ihc.service.pm.MedicineService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 医药药品简拼和排序任务
 */
@Slf4j
@Component
public class MedicinePinyinAndPxSchedule implements ApplicationRunner {

    @Resource
    private MedicineService medicineService;

    @Scheduled(cron = "0 0/5 * * * ?")
    public void execute() {
        medicineService.init();
    }


    /**
     * 项目启动时执行一次
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        execute();
    }
}
