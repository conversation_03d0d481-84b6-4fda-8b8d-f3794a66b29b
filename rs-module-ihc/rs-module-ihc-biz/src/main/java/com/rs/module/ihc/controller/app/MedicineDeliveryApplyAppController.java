package com.rs.module.ihc.controller.app;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.md.vo.MedicineDeliveryApplyAppPageReqVO;
import com.rs.module.ihc.controller.admin.md.vo.MedicineDeliveryApplyAppRespVO;
import com.rs.module.ihc.service.md.MedicineDeliveryApplyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "APP端-药品顾送管理")
@RestController
@RequestMapping("/app/ihc/md/medicineDeliveryApply")
@Validated
public class MedicineDeliveryApplyAppController {

    @Resource
    private MedicineDeliveryApplyService medicineDeliveryApplyService;

    @PostMapping("/page")
    @ApiOperation(value = "获得药品顾送管理-药品顾送申请分页")
    public CommonResult<PageResult<MedicineDeliveryApplyAppRespVO>> getMedicineDeliveryApplyPage(@Valid @RequestBody MedicineDeliveryApplyAppPageReqVO pageReqVO) {
        PageResult<MedicineDeliveryApplyAppRespVO> pageResult = medicineDeliveryApplyService.getMedicineDeliveryApplyPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MedicineDeliveryApplyAppRespVO.class));
    }

}
