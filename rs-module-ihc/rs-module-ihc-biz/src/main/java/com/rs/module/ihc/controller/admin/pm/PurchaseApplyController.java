package com.rs.module.ihc.controller.admin.pm;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.rs.util.DicUtils;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.pm.vo.*;
import com.rs.module.ihc.entity.pm.PurchaseApplyDO;
import com.rs.module.ihc.service.pm.PurchaseApplyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;


@Api(tags = "管理后台 - 药品管理-药品采购申请")
@RestController
@RequestMapping("/ihc/purchase-apply")
@Validated
public class PurchaseApplyController {

    @Resource
    private PurchaseApplyService purchaseApplyService;

    @PostMapping("/create")
    @ApiOperation(value = "创建药品管理-药品采购申请")
    @LogRecordAnnotation(bizModule = "ihc:purchaseApply:create", operateType = LogOperateType.CREATE, title = "创建药品管理-药品采购申请",
    success = "创建药品管理-药品采购申请成功", fail = "创建药品管理-药品采购申请失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createPurchaseApply(@Valid @RequestBody PurchaseApplySaveReqVO createReqVO) {
        return success(purchaseApplyService.createPurchaseApply(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新药品管理-药品采购申请")
    @LogRecordAnnotation(bizModule = "ihc:purchaseApply:update", operateType = LogOperateType.UPDATE, title = "更新药品管理-药品采购申请",
    success = "更新药品管理-药品采购申请成功", fail = "更新药品管理-药品采购申请失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updatePurchaseApply(@Valid @RequestBody PurchaseApplySaveReqVO updateReqVO) {
        purchaseApplyService.updatePurchaseApply(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除药品管理-药品采购申请")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:purchaseApply:delete", operateType = LogOperateType.DELETE, title = "删除药品管理-药品采购申请",
    success = "删除药品管理-药品采购申请成功", fail = "删除药品管理-药品采购申请失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deletePurchaseApply(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           purchaseApplyService.deletePurchaseApply(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得药品管理-药品采购申请")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:purchaseApply:get", operateType = LogOperateType.QUERY, title = "获取药品管理-药品采购申请", bizNo = "{{#id}}", success = "获取药品管理-药品采购申请成功", fail = "获取药品管理-药品采购申请失败", extraInfo = "{{#id}}")
    public CommonResult<PurchaseApplyRespVO> getPurchaseApply(@RequestParam("id") String id) {
        PurchaseApplyRespVO purchaseApply = purchaseApplyService.getPurchaseApply(id);
        String subSystemName = DicUtils.translate("ZD_USER", purchaseApply.getAddUser());

        return success(purchaseApply);
    }

    @GetMapping("/page")
    @ApiOperation(value = "获得药品管理-药品采购申请分页")
    @LogRecordAnnotation(bizModule = "ihc:purchaseApply:page", operateType = LogOperateType.QUERY, title = "获得药品管理-药品采购申请分页",
    success = "获得药品管理-药品采购申请分页成功", fail = "获得药品管理-药品采购申请分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<PurchaseApplyRespVO>> getPurchaseApplyPage(@Valid PurchaseApplyPageReqVO pageReqVO) {
        PageResult<PurchaseApplyDO> pageResult = purchaseApplyService.getPurchaseApplyPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PurchaseApplyRespVO.class));
    }

    @GetMapping("/list")
    @ApiOperation(value = "获得药品管理-药品采购申请列表")
    @LogRecordAnnotation(bizModule = "ihc:purchaseApply:list", operateType = LogOperateType.QUERY, title = "获得药品管理-药品采购申请列表",
    success = "获得药品管理-药品采购申请列表成功", fail = "获得药品管理-药品采购申请列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<PurchaseApplyRespVO>> getPurchaseApplyList(@Valid PurchaseApplyListReqVO listReqVO) {
    List<PurchaseApplyDO> list = purchaseApplyService.getPurchaseApplyList(listReqVO);
        return success(BeanUtils.toBean(list, PurchaseApplyRespVO.class));
    }

    // ==================== 子表（药品管理-药品采购申请-药品信息关联） ====================

    @GetMapping("/purchase-apply-rel/list-by-mlh")
    @ApiOperation(value = "获得药品管理-药品采购申请-药品信息关联列表")
    @ApiImplicitParam(name = "mlh", value = "采购申请id，对应ihc_pm_purchase_apply.id")
    public CommonResult<List<PurchaseApplyRelRespVO>> getPurchaseApplyRelListByMlh(@RequestParam("mlh") String mlh) {
        return success(purchaseApplyService.getPurchaseApplyRelListByMlh(mlh));
    }

    @ApiOperation(value = "获得药品管理-药品采购申请-导出采购清单")
    @ApiImplicitParam(name = "id", value = "采购申请id")
    @GetMapping("/export")
    public void export(HttpServletResponse response,@RequestParam("id") String id) throws IOException {
        List<PurchaseApplyRelRespVO> listByMlh = purchaseApplyService.getPurchaseApplyRelListByMlh(id);
        List<PurchaseApplyRelExportVO> data = BeanUtils.toBean(listByMlh, PurchaseApplyRelExportVO.class);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("采购清单", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        // 创建一个样式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        //设置背景颜色
        headWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        //设置头字体
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short)13);
        headWriteFont.setBold(true);
        headWriteCellStyle.setWriteFont(headWriteFont);
        //设置头居中
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        //内容策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        //设置 水平居中
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);

        EasyExcel.write(response.getOutputStream(), PurchaseApplyRelExportVO.class)
                .registerWriteHandler(horizontalCellStyleStrategy)
                .sheet("采购清单").doWrite(data);
    }


}
