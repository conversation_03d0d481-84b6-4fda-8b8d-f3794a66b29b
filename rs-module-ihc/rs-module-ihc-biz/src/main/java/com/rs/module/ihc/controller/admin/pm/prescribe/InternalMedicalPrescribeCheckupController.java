package com.rs.module.ihc.controller.admin.pm.prescribe;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.pm.prescribe.dto.PrescribeCheckupRegisterDTO;
import com.rs.module.ihc.controller.admin.pm.prescribe.vo.InternalMedicalPrescribeCheckupListReqVO;
import com.rs.module.ihc.controller.admin.pm.prescribe.vo.InternalMedicalPrescribeCheckupPageReqVO;
import com.rs.module.ihc.controller.admin.pm.prescribe.vo.InternalMedicalPrescribeCheckupRespVO;
import com.rs.module.ihc.controller.admin.pm.prescribe.vo.InternalMedicalPrescribeCheckupSaveReqVO;
import com.rs.module.ihc.entity.pm.prescribe.InternalMedicalPrescribeCheckupDO;
import com.rs.module.ihc.service.pm.prescribe.InternalMedicalPrescribeCheckupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "所内就医-处方-体检登记")
@RestController
@RequestMapping("/ihc/internalMedicalPrescribeCheckup")
@Validated
public class InternalMedicalPrescribeCheckupController {

    @Resource
    private InternalMedicalPrescribeCheckupService internalMedicalPrescribeCheckupService;

    @ApiOperation(value = "检查登记")
    @PostMapping("/register")
    public CommonResult<Void> prescribeCheckupRegister(@RequestBody @Valid PrescribeCheckupRegisterDTO prescribeCheckupRegisterDTO) {
        internalMedicalPrescribeCheckupService.prescribeCheckupRegister(prescribeCheckupRegisterDTO);
        return CommonResult.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新所内就医-处方-体检登记")
    public CommonResult<Boolean> updateInternalMedicalPrescribeCheckup(@Valid @RequestBody InternalMedicalPrescribeCheckupSaveReqVO updateReqVO) {
        internalMedicalPrescribeCheckupService.updateInternalMedicalPrescribeCheckup(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除所内就医-处方-体检登记")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteInternalMedicalPrescribeCheckup(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           internalMedicalPrescribeCheckupService.deleteInternalMedicalPrescribeCheckup(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得所内就医-处方-体检登记")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<InternalMedicalPrescribeCheckupRespVO> getInternalMedicalPrescribeCheckup(@RequestParam("id") String id) {
        InternalMedicalPrescribeCheckupDO internalMedicalPrescribeCheckup = internalMedicalPrescribeCheckupService.getInternalMedicalPrescribeCheckup(id);
        return success(BeanUtils.toBean(internalMedicalPrescribeCheckup, InternalMedicalPrescribeCheckupRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得所内就医-处方-体检登记分页")
    public CommonResult<PageResult<InternalMedicalPrescribeCheckupRespVO>> getInternalMedicalPrescribeCheckupPage(@Valid @RequestBody InternalMedicalPrescribeCheckupPageReqVO pageReqVO) {
        PageResult<InternalMedicalPrescribeCheckupDO> pageResult = internalMedicalPrescribeCheckupService.getInternalMedicalPrescribeCheckupPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InternalMedicalPrescribeCheckupRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得所内就医-处方-体检登记列表")
    public CommonResult<List<InternalMedicalPrescribeCheckupRespVO>> getInternalMedicalPrescribeCheckupList(@Valid @RequestBody InternalMedicalPrescribeCheckupListReqVO listReqVO) {
        List<InternalMedicalPrescribeCheckupDO> list = internalMedicalPrescribeCheckupService.getInternalMedicalPrescribeCheckupList(listReqVO);
        return success(BeanUtils.toBean(list, InternalMedicalPrescribeCheckupRespVO.class));
    }
}
