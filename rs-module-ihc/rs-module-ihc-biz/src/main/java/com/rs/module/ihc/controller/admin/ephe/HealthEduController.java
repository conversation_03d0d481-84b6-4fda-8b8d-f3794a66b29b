package com.rs.module.ihc.controller.admin.ephe;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.ephe.vo.HealthEduRespVO;
import com.rs.module.ihc.controller.admin.ephe.vo.HealthEduSaveReqVO;
import com.rs.module.ihc.entity.ephe.HealthEduDO;
import com.rs.module.ihc.service.ephe.HealthEduService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "卫生防疫与健康教育-健康教育")
@RestController
@RequestMapping("/ihc/ephe/healthEdu")
@Validated
public class HealthEduController {

    @Resource
    private HealthEduService healthEduService;

    @PostMapping("/create")
    @ApiOperation(value = "创建卫生防疫与健康教育-健康教育")
    @LogRecordAnnotation(bizModule = "ihc:ephe:healthEdu:create", operateType = LogOperateType.CREATE, title = "卫生防疫与健康教育-健康教育登记",
            success = "卫生防疫与健康教育-健康教育登记-成功", fail = "传染病管理-传染病管理，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createHealthEdu(@Valid @RequestBody HealthEduSaveReqVO createReqVO) {
        return success(healthEduService.createHealthEdu(createReqVO));
    }


    @GetMapping("/get")
    @ApiOperation(value = "获得卫生防疫与健康教育-健康教育")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:ephe:healthEdu-get", operateType = LogOperateType.QUERY, title = "卫生防疫与健康教育-健康教育-信息获取",
            success = "卫生防疫与健康教育-健康教育-信息获取-成功", fail = "卫生防疫与健康教育-健康教育-信息获取-错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<HealthEduRespVO> getHealthEdu(@RequestParam("id") String id) {
        HealthEduDO healthEdu = healthEduService.getHealthEdu(id);
        return success(BeanUtils.toBean(healthEdu, HealthEduRespVO.class));
    }

}
