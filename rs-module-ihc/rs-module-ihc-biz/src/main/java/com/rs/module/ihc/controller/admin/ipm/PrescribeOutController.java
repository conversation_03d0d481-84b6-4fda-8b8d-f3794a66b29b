package com.rs.module.ihc.controller.admin.ipm;

import cn.hutool.core.util.ObjectUtil;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.entity.ipm.PrescribeOutDO;
import com.rs.module.ihc.entity.ipm.PrescribeOutMedicineDO;
import com.rs.module.ihc.service.ipm.PrescribeOutService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 所内就医-处方药品出库记录")
@RestController
@RequestMapping("/ihc/ipm/prescribeOut")
@Validated
public class PrescribeOutController {

    @Resource
    private PrescribeOutService prescribeOutService;

    @PostMapping("/create")
    @ApiOperation(value = "创建所内就医-处方药品出库记录")
    @LogRecordAnnotation(bizModule = "ihc:prescribeOut:create", operateType = LogOperateType.CREATE, title = "创建所内就医-处方药品出库记录",
            success = "创建所内就医-处方药品出库记录成功", fail = "创建所内就医-处方药品出库记录失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createPrescribeOut(@Valid @RequestBody PrescribeOutSaveReqVO createReqVO) {
        return success(prescribeOutService.createPrescribeOut(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新所内就医-处方药品出库记录")
    @LogRecordAnnotation(bizModule = "ihc:prescribeOut:update", operateType = LogOperateType.UPDATE, title = "更新所内就医-处方药品出库记录",
            success = "更新所内就医-处方药品出库记录成功", fail = "更新所内就医-处方药品出库记录失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updatePrescribeOut(@Valid @RequestBody PrescribeOutSaveReqVO updateReqVO) {
        prescribeOutService.updatePrescribeOut(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除所内就医-处方药品出库记录")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:prescribeOut:delete", operateType = LogOperateType.DELETE, title = "删除所内就医-处方药品出库记录",
            success = "删除所内就医-处方药品出库记录成功", fail = "删除所内就医-处方药品出库记录失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deletePrescribeOut(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            prescribeOutService.deletePrescribeOut(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得所内就医-处方药品出库记录")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:prescribeOut:get", operateType = LogOperateType.QUERY, title = "获取所内就医-处方药品出库记录", bizNo = "{{#id}}", success = "获取所内就医-处方药品出库记录成功", fail = "获取所内就医-处方药品出库记录失败", extraInfo = "{{#id}}")
    public CommonResult<PrescribeOutRespVO> getPrescribeOut(@RequestParam("id") String id) {
        PrescribeOutRespVO outRespVO = prescribeOutService.getPrescribeOutById(id);
        if (ObjectUtil.isEmpty(outRespVO)) {
            return success();
        }
        List<PrescribeOutMedicineDO> list = prescribeOutService.getPrescribeOutMedicineListByOutId(outRespVO.getId());
        outRespVO.setPrescribeOutMedicines(BeanUtils.toBean(list, PrescribeOutMedicineRespVO.class));
        return success(outRespVO);
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得所内就医-处方药品出库记录分页")
    @LogRecordAnnotation(bizModule = "ihc:prescribeOut:page", operateType = LogOperateType.QUERY, title = "获得所内就医-处方药品出库记录分页",
            success = "获得所内就医-处方药品出库记录分页成功", fail = "获得所内就医-处方药品出库记录分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<PrescribeOutRespVO>> getPrescribeOutPage(@Valid @RequestBody PrescribeOutPageReqVO pageReqVO) {
        PageResult<PrescribeOutDO> pageResult = prescribeOutService.getPrescribeOutPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PrescribeOutRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得所内就医-处方药品出库记录列表")
    @LogRecordAnnotation(bizModule = "ihc:prescribeOut:list", operateType = LogOperateType.QUERY, title = "获得所内就医-处方药品出库记录列表",
            success = "获得所内就医-处方药品出库记录列表成功", fail = "获得所内就医-处方药品出库记录列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<PrescribeOutRespVO>> getPrescribeOutList(@Valid @RequestBody PrescribeOutListReqVO listReqVO) {
        List<PrescribeOutDO> list = prescribeOutService.getPrescribeOutList(listReqVO);
        return success(BeanUtils.toBean(list, PrescribeOutRespVO.class));
    }

    // ==================== 子表（所内就医-处方药品出库记录关联药品） ====================

    @GetMapping("/prescribe-out-medicine/list-by-out-id")
    @ApiOperation(value = "获得所内就医-处方药品出库记录关联药品列表")
    @ApiImplicitParam(name = "outId", value = "处方药品出库id，对应iihc_ipm_prescribe_out.id")
    public CommonResult<List<PrescribeOutMedicineDO>> getPrescribeOutMedicineListByOutId(@RequestParam("outId") String outId) {
        return success(prescribeOutService.getPrescribeOutMedicineListByOutId(outId));
    }

    @ApiOperation(value = "处方药品出库")
    @PostMapping("/medicine")
    public CommonResult<Void> prescribeMedicineOut(@RequestBody @Valid PrescribeMedicineOutReqVO prescribeMedicineOutDTO) {
        prescribeOutService.prescribeMedicineOut(prescribeMedicineOutDTO);
        return CommonResult.success();
    }
    @ApiOperation(value = "批量处方药品出库")
    @PostMapping("/v2/medicine")
    public CommonResult<Void> prescribeMedicineOutV2(@RequestBody @Valid PrescribeMedicineOutInfoV2 prescribeMedicineOutDTO) {
        for (PrescribeMedicineOutInfoV2List prescribeMedicineOutInfoV2 : prescribeMedicineOutDTO.getPrescribeMedicineOutInfoList()) {
            PrescribeMedicineOutReqVO prescribeMedicineOutReqVO = new PrescribeMedicineOutReqVO();
            prescribeMedicineOutReqVO.setPrescribeId(prescribeMedicineOutInfoV2.getPrescribeId());
            List<PrescribeMedicineOutReqVO.PrescribeMedicineOutInfo> list = new ArrayList<>();
            list.add(BeanUtils.toBean(prescribeMedicineOutInfoV2, PrescribeMedicineOutReqVO.PrescribeMedicineOutInfo.class));
            prescribeMedicineOutReqVO.setPrescribeMedicineOutInfoList(list);
            prescribeOutService.prescribeMedicineOut(prescribeMedicineOutReqVO);
        }
        return CommonResult.success();
    }

}
