package com.rs.module.ihc.task.pm;

import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineCodeImportErrorVO;
import com.rs.module.ihc.controller.admin.pm.vo.MedicineCodeImportVO;
import com.rs.module.ihc.handler.pm.IhcPmMedicineCodeToDb;
import com.rs.module.ihc.handler.pm.IhcPmMedicineCodeToDic;
import com.rs.module.ihc.handler.pm.IhcPmMedicineCodeValidate;
import com.rs.module.ihc.service.pm.IhcPmMedicineCodeBuilder;
import com.rs.module.ihc.service.pm.IhcPmMedicineCodeService;
import lombok.extern.log4j.Log4j2;

import java.util.Map;
import java.util.concurrent.Callable;

/**
 * @ClassName MedicineCodeImportTask
 * 
 * <AUTHOR>
 * @Date 2025/3/18 16:26
 * @Version 1.0
 */
@Log4j2
public  class MedicineCodeImportTask implements Callable<MedicineCodeImportErrorVO> {
    private final MedicineCodeImportVO medicineCodeImportVO;
    private final Map<String, String> dosageFormDic;
    private final IhcPmMedicineCodeService ihcPmMedicineCodeService;

    public MedicineCodeImportTask(MedicineCodeImportVO medicineCodeImportVO, Map<String, String> dosageFormDic, IhcPmMedicineCodeService ihcPmMedicineCodeService) {
        this.medicineCodeImportVO = medicineCodeImportVO;
        this.dosageFormDic = dosageFormDic;
        this.ihcPmMedicineCodeService = ihcPmMedicineCodeService;
    }

    @Override
    public MedicineCodeImportErrorVO call() {
        try {
            new IhcPmMedicineCodeBuilder.Builder().
                    linkWith(new IhcPmMedicineCodeValidate()).
                    linkWith(new IhcPmMedicineCodeToDic(dosageFormDic)).
                    linkWith(new IhcPmMedicineCodeToDb(ihcPmMedicineCodeService)).
                    build().importExcel(medicineCodeImportVO);
            return null; // 如果成功，返回null
        } catch (Exception e) {
            log.error("导入药品失败", e);
            MedicineCodeImportErrorVO medicineCodeImportErrorVO = BeanUtils.toBean(medicineCodeImportVO, MedicineCodeImportErrorVO.class);
            medicineCodeImportErrorVO.setErrorMsg(e.getMessage());
            return medicineCodeImportErrorVO; // 如果失败，返回错误信息
        }
    }
}
