package com.rs.module.ihc.controller.admin.hc;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.ihc.controller.admin.hc.vo.*;
import com.rs.module.ihc.entity.hc.HealthCheckupFileDO;
import com.rs.module.ihc.service.hc.HealthCheckupFileService;

@Api(tags = "五项体检-附件")
@RestController
@RequestMapping("/ihc/hc/healthCheckupFile")
@Validated
public class HealthCheckupFileController {

    @Resource
    private HealthCheckupFileService healthCheckupFileService;

    @PostMapping("/create")
    @ApiOperation(value = "创建五项体检-附件")
    public CommonResult<String> createHealthCheckupFile(@Valid @RequestBody HealthCheckupFileSaveReqVO createReqVO) {
        return success(healthCheckupFileService.createHealthCheckupFile(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新五项体检-附件")
    public CommonResult<Boolean> updateHealthCheckupFile(@Valid @RequestBody HealthCheckupFileSaveReqVO updateReqVO) {
        healthCheckupFileService.updateHealthCheckupFile(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除五项体检-附件")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteHealthCheckupFile(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           healthCheckupFileService.deleteHealthCheckupFile(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得五项体检-附件")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<HealthCheckupFileRespVO> getHealthCheckupFile(@RequestParam("id") String id) {
        HealthCheckupFileDO healthCheckupFile = healthCheckupFileService.getHealthCheckupFile(id);
        return success(BeanUtils.toBean(healthCheckupFile, HealthCheckupFileRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得五项体检-附件分页")
    public CommonResult<PageResult<HealthCheckupFileRespVO>> getHealthCheckupFilePage(@Valid @RequestBody HealthCheckupFilePageReqVO pageReqVO) {
        PageResult<HealthCheckupFileDO> pageResult = healthCheckupFileService.getHealthCheckupFilePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, HealthCheckupFileRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得五项体检-附件列表")
    public CommonResult<List<HealthCheckupFileRespVO>> getHealthCheckupFileList(@Valid @RequestBody HealthCheckupFileListReqVO listReqVO) {
        List<HealthCheckupFileDO> list = healthCheckupFileService.getHealthCheckupFileList(listReqVO);
        return success(BeanUtils.toBean(list, HealthCheckupFileRespVO.class));
    }
}
