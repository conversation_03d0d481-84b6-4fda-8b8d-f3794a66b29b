package com.rs.module.ihc.controller.admin.pm.sick;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.pm.sick.vo.SeverelySickReliveApplyListReqVO;
import com.rs.module.ihc.controller.admin.pm.sick.vo.SeverelySickReliveApplyPageReqVO;
import com.rs.module.ihc.controller.admin.pm.sick.vo.SeverelySickReliveApplyRespVO;
import com.rs.module.ihc.controller.admin.pm.sick.vo.SeverelySickReliveApplySaveReqVO;
import com.rs.module.ihc.entity.pm.sick.SeverelySickReliveApplyDO;
import com.rs.module.ihc.service.pm.sick.SeverelySickReliveApplyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "重特病号解除申请")
@RestController
@RequestMapping("/ihc/pm.sick/severelySickReliveApply")
@Validated
public class SeverelySickReliveApplyController {

    @Resource
    private SeverelySickReliveApplyService severelySickReliveApplyService;

    @PostMapping("/create")
    @ApiOperation(value = "创建重特病号解除申请")
    public CommonResult<String> createSeverelySickReliveApply(@Valid @RequestBody SeverelySickReliveApplySaveReqVO createReqVO) {
        return success(severelySickReliveApplyService.createSeverelySickReliveApply(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新重特病号解除申请")
    public CommonResult<Boolean> updateSeverelySickReliveApply(@Valid @RequestBody SeverelySickReliveApplySaveReqVO updateReqVO) {
        severelySickReliveApplyService.updateSeverelySickReliveApply(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除重特病号解除申请")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteSeverelySickReliveApply(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           severelySickReliveApplyService.deleteSeverelySickReliveApply(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得重特病号解除申请")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<SeverelySickReliveApplyRespVO> getSeverelySickReliveApply(@RequestParam("id") String id) {
        SeverelySickReliveApplyDO severelySickReliveApply = severelySickReliveApplyService.getSeverelySickReliveApply(id);
        return success(BeanUtils.toBean(severelySickReliveApply, SeverelySickReliveApplyRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得重特病号解除申请分页")
    public CommonResult<PageResult<SeverelySickReliveApplyRespVO>> getSeverelySickReliveApplyPage(@Valid @RequestBody SeverelySickReliveApplyPageReqVO pageReqVO) {
        PageResult<SeverelySickReliveApplyDO> pageResult = severelySickReliveApplyService.getSeverelySickReliveApplyPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SeverelySickReliveApplyRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得重特病号解除申请列表")
    public CommonResult<List<SeverelySickReliveApplyRespVO>> getSeverelySickReliveApplyList(@Valid @RequestBody SeverelySickReliveApplyListReqVO listReqVO) {
        List<SeverelySickReliveApplyDO> list = severelySickReliveApplyService.getSeverelySickReliveApplyList(listReqVO);
        return success(BeanUtils.toBean(list, SeverelySickReliveApplyRespVO.class));
    }
}
