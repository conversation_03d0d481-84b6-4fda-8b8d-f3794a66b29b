package com.rs.module.ihc.controller.admin.ipm;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.ihc.controller.admin.ipm.vo.*;
import com.rs.module.ihc.entity.ipm.PrescribeMedicineDO;
import com.rs.module.ihc.service.ipm.PrescribeMedicineService;

@Api(tags = "管理后台 - 所内就医-处方-药方信息")
@RestController
@RequestMapping("/ihc/ipm/prescribe-medicine")
@Validated
public class PrescribeMedicineController {

    @Resource
    private PrescribeMedicineService prescribeMedicineService;

    @PostMapping("/create")
    @ApiOperation(value = "创建所内就医-处方-药方信息")
    @LogRecordAnnotation(bizModule = "ihc:prescribeMedicine:create", operateType = LogOperateType.CREATE, title = "创建所内就医-处方-药方信息",
    success = "创建所内就医-处方-药方信息成功", fail = "创建所内就医-处方-药方信息失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createPrescribeMedicine(@Valid @RequestBody PrescribeMedicineSaveReqVO createReqVO) {
        return success(prescribeMedicineService.createPrescribeMedicine(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新所内就医-处方-药方信息")
    @LogRecordAnnotation(bizModule = "ihc:prescribeMedicine:update", operateType = LogOperateType.UPDATE, title = "更新所内就医-处方-药方信息",
    success = "更新所内就医-处方-药方信息成功", fail = "更新所内就医-处方-药方信息失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updatePrescribeMedicine(@Valid @RequestBody PrescribeMedicineSaveReqVO updateReqVO) {
        prescribeMedicineService.updatePrescribeMedicine(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除所内就医-处方-药方信息")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:prescribeMedicine:delete", operateType = LogOperateType.DELETE, title = "删除所内就医-处方-药方信息",
    success = "删除所内就医-处方-药方信息成功", fail = "删除所内就医-处方-药方信息失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deletePrescribeMedicine(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           prescribeMedicineService.deletePrescribeMedicine(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得所内就医-处方-药方信息")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "ihc:prescribeMedicine:get", operateType = LogOperateType.QUERY, title = "获取所内就医-处方-药方信息", bizNo = "{{#id}}", success = "获取所内就医-处方-药方信息成功", fail = "获取所内就医-处方-药方信息失败", extraInfo = "{{#id}}")
    public CommonResult<PrescribeMedicineRespVO> getPrescribeMedicine(@RequestParam("id") String id) {
        PrescribeMedicineDO prescribeMedicine = prescribeMedicineService.getPrescribeMedicine(id);
        return success(BeanUtils.toBean(prescribeMedicine, PrescribeMedicineRespVO.class));
    }


}
