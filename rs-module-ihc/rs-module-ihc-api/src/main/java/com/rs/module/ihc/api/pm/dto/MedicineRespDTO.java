package com.rs.module.ihc.api.pm.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "RPC服务-药品Response DTO")
@Data
public class MedicineRespDTO {

	@Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "110")
    private String id;

    @Schema(description = "药品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "硫酸沙丁胺醇吸入气雾剂")
    private String medicineName;
    
    @Schema(description = "药品名称全拼", requiredMode = Schema.RequiredMode.REQUIRED, example = "liusuanshadinganchunxiruqiwuji")
    private String medicineSpellName;
    
    @Schema(description = "药品名称拼音首字母或者英文拼接", requiredMode = Schema.RequiredMode.REQUIRED, example = "lssdacxrqwj")
    private String medicineNameFirstLetter;

    @Schema(description = "剂型（字典:剂型）", requiredMode = Schema.RequiredMode.REQUIRED, example = "8")
    private String dosageForm;

    @Schema(description = "计量单位（字典：计量单位）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String measurementUnit;

    @Schema(description = "规格", requiredMode = Schema.RequiredMode.REQUIRED, example = "100ug/揿*200揿/瓶")
    private String specs;

    @Schema(description = "生产单位", requiredMode = Schema.RequiredMode.REQUIRED, example = "山东京卫制药有限公司")
    private String productUnit;    
    
    @Schema(description = "批准文号", requiredMode = Schema.RequiredMode.REQUIRED, example = "国药准字H20113348")
    private String approvalNum;

    @Schema(description = "监所id", requiredMode = Schema.RequiredMode.REQUIRED, example = "440183111")
    private String prisonId;
}
