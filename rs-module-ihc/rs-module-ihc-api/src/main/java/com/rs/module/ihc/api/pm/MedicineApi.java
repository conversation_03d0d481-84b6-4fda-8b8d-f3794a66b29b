package com.rs.module.ihc.api.pm;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.ihc.api.pm.dto.MedicineRespDTO;
import com.rs.module.ihc.enums.ApiConstants;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC服务-药品")
public interface MedicineApi {

	String PREFIX = ApiConstants.PREFIX + "/medicine";
	
	@GetMapping(PREFIX + "/get")
	@Operation(summary = "获取药品信息")
	@Parameter(name = "id", description = "药品Id", example = "110", required = true)
	CommonResult<MedicineRespDTO> getMedicine(@RequestParam("id") String id);
}
