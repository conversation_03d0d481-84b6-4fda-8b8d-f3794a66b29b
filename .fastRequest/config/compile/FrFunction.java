import com.github.javafaker.Faker;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;
import java.util.UUID;

public class FrFunction {
    private static final String CHAR_LOWER = "abcdefghijklmnopqrstuvwxyz";
    private static final String CHAR_UPPER = CHAR_LOWER.toUpperCase();
    private static final String CHAR_NUMBER = "0123456789";
    private static final String CHARS_SPECIAL = "!#$%&*()_+-=[]|,.?><";
    private static final String CHAR_NORMAL = CHAR_LOWER + CHAR_UPPER + CHAR_NUMBER;

    private static final SecureRandom RANDOM = new SecureRandom();
    private static final Faker FAKER = new Faker(Locale.CHINA);

    /**
     * ??????????
     */
    public int randomInt() {
        return RANDOM.nextInt(100);
    }

    /**
     * ???????????
     */
    public boolean randomBoolean() {
        return RANDOM.nextBoolean();
    }

    /**
     * ????????????
     */
    public float randomFloat() {
        return RANDOM.nextFloat();
    }

    /**
     * ?????????
     */
    public char randomChar() {
        return CHAR_LOWER.charAt(RANDOM.nextInt(26));
    }

    /**
     * ?????????????
     */
    public char randomChineseChar() {
        return (char) (0x4E00 + RANDOM.nextInt(0x9FFF - 0x4E00 + 1));
    }

    /**
     * ??????uuid
     */
    public String uuid() {
        return UUID.randomUUID().toString();
    }

    /**
     * ????????????????
     */
    public String randomString() {
        StringBuilder sb = new StringBuilder(10);
        for (int i = 0; i < 10; i++) {
            int randomIndex = RANDOM.nextInt(CHAR_NORMAL.length());
            sb.append(CHAR_NORMAL.charAt(randomIndex));
        }
        return sb.toString();
    }

    /**
     * ??????yyyy-MM-dd HH:mm:ss
     */
    public String dateTime() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * ???????yyyy-MM-dd
     */
    public String date() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    /**
     * ??????HH:mm:ss
     */
    public String time() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
    }

    /**
     * ???????
     */
    public long currentTimeMillis() {
        return System.currentTimeMillis();
    }

    /**
     * ???
     */
    public String address() {
        return FAKER.address().fullAddress();
    }


    /**
     * ????
     */
    public String name() {
        return FAKER.name().name();
    }

    /**
     * ????
     */
    public String email() {
        return FAKER.internet().emailAddress();
    }

    /**
     * ?????
     */
    public String phone() {
        return FAKER.phoneNumber().cellPhone();
    }

    /**
     * ????
     */
    public String password() {
        return FAKER.internet().password();
    }

}
