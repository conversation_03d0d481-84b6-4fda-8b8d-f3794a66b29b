<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
	<parent>
		<groupId>com.rs</groupId>
		<artifactId>rs-module-infra</artifactId>
		<version>${rs.version}</version>
	</parent>
	
	<artifactId>rs-module-infra-api</artifactId>
	<packaging>jar</packaging>
	<name>${project.artifactId}</name>
	<description>监所管理-基础设施-API，暴露给其它模块调用</description>
	
	<dependencies>
		
		<!-- web 相关 begin -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-ui</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <optional>true</optional>
        </dependency>
        <!-- web 相关 end -->
        
		<!-- 工具类相关 begin -->
		<dependency>
			<groupId>com.rs</groupId>
			<artifactId>rs-common</artifactId>
		</dependency>
		<!-- 工具类相关 end -->
	</dependencies> 
</project>